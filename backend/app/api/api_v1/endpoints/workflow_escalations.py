"""
Workflow Escalations API endpoints for Averum Contracts

Provides endpoints for managing workflow escalations including:
- Manual escalation triggers
- Escalation status tracking
- Timeout management
- Escalation analytics
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import logging

from app.auth.clerk_auth import get_current_user
from app.services.approval_workflow_service import approval_workflow_service, EscalationType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/workflow-escalations", tags=["workflow-escalations"])


class EscalationCreate(BaseModel):
    """Schema for creating manual escalations."""
    workflow_id: str
    escalation_type: str = Field(..., regex="^(timeout|manual|automatic|conditional)$")
    reason: Optional[str] = Field(None, max_length=1000)


class EscalationResolve(BaseModel):
    """Schema for resolving escalations."""
    resolution_notes: Optional[str] = Field(None, max_length=1000)


@router.post("/", response_model=Dict[str, Any])
async def create_escalation(
    escalation_data: EscalationCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a manual escalation for a workflow."""
    try:
        escalation_type = EscalationType(escalation_data.escalation_type)
        
        result = await approval_workflow_service.handle_escalation(
            escalation_data.workflow_id,
            escalation_type,
            escalation_data.reason
        )
        
        if result.get("error"):
            if "not found" in result["error"].lower():
                raise HTTPException(status_code=404, detail=result["error"])
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "Escalation created successfully",
            "data": result
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid escalation type: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating escalation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflow/{workflow_id}", response_model=Dict[str, Any])
async def get_workflow_escalations(
    workflow_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get all escalations for a specific workflow."""
    try:
        # Get escalations from database
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        
        escalations_response = supabase.table("workflow_escalations").select("*").eq(
            "workflow_id", workflow_id
        ).order("created_at", desc=True).execute()
        
        escalations = escalations_response.data or []
        
        return {
            "success": True,
            "data": escalations,
            "count": len(escalations)
        }
        
    except Exception as e:
        logger.error(f"Error getting workflow escalations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=Dict[str, Any])
async def get_escalations(
    status: Optional[str] = Query(None, description="Filter by escalation status"),
    escalation_type: Optional[str] = Query(None, description="Filter by escalation type"),
    days: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    current_user: dict = Depends(get_current_user)
):
    """Get escalations for the current workspace."""
    try:
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        
        # Calculate date range
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Build query
        query = supabase.table("workflow_escalations").select(
            "*, approval_workflows!inner(workspace_id, contract_id, contracts(title))"
        ).gte("created_at", start_date.isoformat())
        
        # Apply filters
        if escalation_type:
            query = query.eq("escalation_type", escalation_type)
        
        # Filter by workspace through the workflow relationship
        workspace_id = current_user.get("workspace_id")
        if workspace_id:
            query = query.eq("approval_workflows.workspace_id", workspace_id)
        
        escalations_response = query.order("created_at", desc=True).execute()
        escalations = escalations_response.data or []
        
        # Add status filter (since resolved_at indicates resolution)
        if status:
            if status == "resolved":
                escalations = [e for e in escalations if e.get("resolved_at")]
            elif status == "pending":
                escalations = [e for e in escalations if not e.get("resolved_at")]
        
        return {
            "success": True,
            "data": escalations,
            "count": len(escalations),
            "filters": {
                "status": status,
                "escalation_type": escalation_type,
                "days": days
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting escalations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{escalation_id}/resolve", response_model=Dict[str, Any])
async def resolve_escalation(
    escalation_id: str,
    resolution_data: EscalationResolve,
    current_user: dict = Depends(get_current_user)
):
    """Resolve an escalation."""
    try:
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        
        # Update escalation as resolved
        update_data = {
            "resolved_at": datetime.utcnow().isoformat(),
            "resolved_by": current_user["user_id"],
            "resolution_notes": resolution_data.resolution_notes,
            "updated_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table("workflow_escalations").update(update_data).eq(
            "id", escalation_id
        ).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Escalation not found")
        
        return {
            "success": True,
            "message": "Escalation resolved successfully",
            "data": result.data[0]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving escalation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/check-timeouts", response_model=Dict[str, Any])
async def check_timeouts(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Manually trigger timeout checking for all workflows."""
    try:
        # Run timeout check in background
        background_tasks.add_task(run_timeout_check)
        
        return {
            "success": True,
            "message": "Timeout check initiated in background"
        }
        
    except Exception as e:
        logger.error(f"Error initiating timeout check: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def run_timeout_check():
    """Background task to check for timeouts."""
    try:
        result = await approval_workflow_service.check_timeouts()
        logger.info(f"Timeout check completed: {result}")
    except Exception as e:
        logger.error(f"Error in timeout check background task: {str(e)}")


@router.get("/analytics", response_model=Dict[str, Any])
async def get_escalation_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: dict = Depends(get_current_user)
):
    """Get escalation analytics for the workspace."""
    try:
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        
        workspace_id = current_user.get("workspace_id")
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get escalations with workflow data
        escalations_response = supabase.table("workflow_escalations").select(
            "*, approval_workflows!inner(workspace_id, created_at)"
        ).eq("approval_workflows.workspace_id", workspace_id).gte(
            "created_at", start_date.isoformat()
        ).execute()
        
        escalations = escalations_response.data or []
        
        # Calculate analytics
        total_escalations = len(escalations)
        escalation_types = {}
        resolved_escalations = 0
        avg_resolution_time = 0
        
        resolution_times = []
        
        for escalation in escalations:
            # Count by type
            esc_type = escalation.get("escalation_type", "unknown")
            escalation_types[esc_type] = escalation_types.get(esc_type, 0) + 1
            
            # Check if resolved
            if escalation.get("resolved_at"):
                resolved_escalations += 1
                
                # Calculate resolution time
                created_at = datetime.fromisoformat(escalation["created_at"].replace('Z', '+00:00'))
                resolved_at = datetime.fromisoformat(escalation["resolved_at"].replace('Z', '+00:00'))
                resolution_time = (resolved_at - created_at).total_seconds() / 3600  # hours
                resolution_times.append(resolution_time)
        
        if resolution_times:
            avg_resolution_time = sum(resolution_times) / len(resolution_times)
        
        # Get total workflows for escalation rate
        workflows_response = supabase.table("approval_workflows").select("id").eq(
            "workspace_id", workspace_id
        ).gte("created_at", start_date.isoformat()).execute()
        
        total_workflows = len(workflows_response.data or [])
        escalation_rate = (total_escalations / total_workflows * 100) if total_workflows > 0 else 0
        
        return {
            "success": True,
            "data": {
                "period_days": days,
                "total_escalations": total_escalations,
                "total_workflows": total_workflows,
                "escalation_rate_percent": round(escalation_rate, 2),
                "resolved_escalations": resolved_escalations,
                "resolution_rate_percent": round((resolved_escalations / total_escalations * 100) if total_escalations > 0 else 0, 2),
                "average_resolution_time_hours": round(avg_resolution_time, 2),
                "escalation_types": escalation_types,
                "trends": {
                    "daily_escalations": [],  # Could be calculated with more complex query
                    "resolution_trend": []
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting escalation analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pending-timeouts", response_model=Dict[str, Any])
async def get_pending_timeouts(
    current_user: dict = Depends(get_current_user)
):
    """Get workflows that are approaching timeout or have timed out."""
    try:
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        
        workspace_id = current_user.get("workspace_id")
        
        # Get active workflows with pending approval steps
        workflows_response = supabase.table("approval_workflows").select(
            "*, approval_steps!inner(*), contracts(title)"
        ).eq("workspace_id", workspace_id).eq("status", "active").eq(
            "approval_steps.status", "pending"
        ).execute()
        
        workflows = workflows_response.data or []
        
        timeout_info = []
        current_time = datetime.utcnow()
        
        for workflow in workflows:
            for step in workflow.get("approval_steps", []):
                if step["status"] == "pending":
                    created_at = datetime.fromisoformat(step["created_at"].replace('Z', '+00:00'))
                    timeout_hours = step.get("timeout_hours", 24)
                    timeout_at = created_at + timedelta(hours=timeout_hours)
                    
                    # Calculate time remaining
                    time_remaining = timeout_at.replace(tzinfo=None) - current_time
                    hours_remaining = time_remaining.total_seconds() / 3600
                    
                    # Include if timed out or within 4 hours of timeout
                    if hours_remaining <= 4:
                        timeout_info.append({
                            "workflow_id": workflow["id"],
                            "contract_title": workflow.get("contracts", {}).get("title", "Unknown"),
                            "step_id": step["id"],
                            "approver_role": step.get("approver_role"),
                            "created_at": step["created_at"],
                            "timeout_hours": timeout_hours,
                            "hours_remaining": round(hours_remaining, 2),
                            "is_overdue": hours_remaining <= 0,
                            "urgency": "critical" if hours_remaining <= 0 else "high" if hours_remaining <= 2 else "medium"
                        })
        
        # Sort by urgency (overdue first, then by time remaining)
        timeout_info.sort(key=lambda x: (not x["is_overdue"], x["hours_remaining"]))
        
        return {
            "success": True,
            "data": timeout_info,
            "count": len(timeout_info),
            "overdue_count": len([t for t in timeout_info if t["is_overdue"]]),
            "urgent_count": len([t for t in timeout_info if t["urgency"] in ["critical", "high"]])
        }
        
    except Exception as e:
        logger.error(f"Error getting pending timeouts: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
