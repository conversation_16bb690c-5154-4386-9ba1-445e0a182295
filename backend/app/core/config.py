"""
Configuration settings for the Averum Contracts backend application.
"""

import os
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Configuration
    API_PREFIX: str = "/api"
    PROJECT_NAME: str = "Averum Contracts API"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    
    # Supabase Configuration
    SUPABASE_URL: str
    SUPABASE_KEY: str
    
    # Clerk Configuration
    CLERK_SECRET_KEY: str
    CLERK_PUBLISHABLE_KEY: str
    CLERK_WEBHOOK_SECRET: Optional[str] = None
    
    # CORS Configuration
    BACKEND_CORS_ORIGINS: List[str] = []
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """Parse CORS origins from environment variable."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            if isinstance(v, str):
                import json
                return json.loads(v)
            return v
        raise ValueError(v)
    
    # Database Configuration
    DATABASE_URL: Optional[str] = None
    
    # Security Configuration
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Rate Limiting Configuration
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 1000
    
    # Storage Configuration
    STORAGE_BUCKET_NAME: str = "averum-contracts-documents"
    
    # AI Configuration
    HUGGINGFACE_API_KEY: Optional[str] = None
    GEMINI_API_KEY: Optional[str] = None
    LEGALBERT_MODEL: str = "nlpaueb/legal-bert-base-uncased"
    GEMINI_MODEL: str = "gemini-1.5-pro-latest"
    AI_CACHE_TTL: int = 3600  # 1 hour cache for AI results
    AI_MAX_RETRIES: int = 3
    AI_TIMEOUT: int = 60  # seconds

    # Redis Configuration (for AI caching)
    REDIS_URL: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0

    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

    def validate_ai_configuration(self) -> bool:
        """Validate AI service configuration."""
        if not self.HUGGINGFACE_API_KEY:
            print("WARNING: HUGGINGFACE_API_KEY not configured. AI features will be limited.")
            return False

        if not self.GEMINI_API_KEY:
            print("WARNING: GEMINI_API_KEY not configured. Fallback AI features will be limited.")
            return False

        return True

    def validate_required_settings(self) -> bool:
        """Validate all required settings for production."""
        errors = []

        # Required for production
        if self.ENVIRONMENT == "production":
            if not self.SUPABASE_URL or self.SUPABASE_URL == "your_supabase_url_here":
                errors.append("SUPABASE_URL must be configured for production")

            if not self.SUPABASE_KEY or self.SUPABASE_KEY == "your_supabase_anon_key_here":
                errors.append("SUPABASE_KEY must be configured for production")

            if not self.CLERK_SECRET_KEY or self.CLERK_SECRET_KEY == "sk_test_placeholder_get_from_clerk_dashboard":
                errors.append("CLERK_SECRET_KEY must be configured for production")

        # AI configuration warnings
        if not self.validate_ai_configuration():
            print("AI services may not function properly without proper API keys")

        if errors:
            print("CONFIGURATION ERRORS:")
            for error in errors:
                print(f"  - {error}")
            return False

        return True


# Create global settings instance
settings = Settings()

# Validate settings on startup
if not settings.validate_required_settings():
    print("WARNING: Configuration validation failed. Some features may not work properly.")
