import React, { useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import type { NotificationType } from '@/services/api-types';

interface NotificationToastOptions {
  title: string;
  message: string;
  type: NotificationType;
  actionUrl?: string;
  duration?: number;
  persistent?: boolean;
}

interface UseNotificationToastReturn {
  showNotificationToast: (options: NotificationToastOptions) => void;
  showSuccessToast: (title: string, message?: string) => void;
  showErrorToast: (title: string, message?: string) => void;
  showInfoToast: (title: string, message?: string) => void;
  showWarningToast: (title: string, message?: string) => void;
}

export function useNotificationToast(): UseNotificationToastReturn {
  const { toast } = useToast();

  // Get toast variant based on notification type
  const getToastVariant = (type: NotificationType): "default" | "destructive" => {
    switch (type) {
      case 'approval':
        return 'default';
      case 'contract':
        return 'default';
      case 'mention':
        return 'default';
      case 'system':
        return 'default';
      default:
        return 'default';
    }
  };

  // Get icon for notification type
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'approval':
        return '✅';
      case 'contract':
        return '📄';
      case 'mention':
        return '💬';
      case 'system':
        return '🔔';
      default:
        return '📢';
    }
  };

  // Show notification toast
  const showNotificationToast = useCallback((options: NotificationToastOptions) => {
    const { title, message, type, actionUrl, duration, persistent } = options;
    
    const icon = getNotificationIcon(type);
    const variant = getToastVariant(type);
    
    toast({
      title: `${icon} ${title}`,
      description: message,
      variant,
      duration: persistent ? Infinity : (duration || 5000),
      action: actionUrl ? (
        <button
          onClick={() => {
            window.location.href = actionUrl;
          }}
          className="inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50"
        >
          View
        </button>
      ) : undefined,
    });
  }, [toast]);

  // Show success toast
  const showSuccessToast = useCallback((title: string, message?: string) => {
    toast({
      title: `✅ ${title}`,
      description: message,
      variant: 'default',
      duration: 3000,
    });
  }, [toast]);

  // Show error toast
  const showErrorToast = useCallback((title: string, message?: string) => {
    toast({
      title: `❌ ${title}`,
      description: message,
      variant: 'destructive',
      duration: 5000,
    });
  }, [toast]);

  // Show info toast
  const showInfoToast = useCallback((title: string, message?: string) => {
    toast({
      title: `ℹ️ ${title}`,
      description: message,
      variant: 'default',
      duration: 4000,
    });
  }, [toast]);

  // Show warning toast
  const showWarningToast = useCallback((title: string, message?: string) => {
    toast({
      title: `⚠️ ${title}`,
      description: message,
      variant: 'default',
      duration: 4000,
    });
  }, [toast]);

  return {
    showNotificationToast,
    showSuccessToast,
    showErrorToast,
    showInfoToast,
    showWarningToast,
  };
}
