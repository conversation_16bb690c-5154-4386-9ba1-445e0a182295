import { useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@clerk/clerk-react';
import { useWorkspaceStore, useWorkspaces as useWorkspacesFromStore, useWorkspaceLoading, useWorkspaceError } from '../lib/workspace-store';
import { centralizedWorkspaceService } from '../services/workspace-service';
import { Workspace } from '../types/workspace';

interface UseWorkspacesOptions {
  /**
   * Whether to automatically fetch workspaces on mount
   */
  autoFetch?: boolean;
  
  /**
   * Whether to refetch when auth state changes
   */
  refetchOnAuth?: boolean;
  
  /**
   * Callback when workspaces are successfully loaded
   */
  onSuccess?: (workspaces: Workspace[]) => void;
  
  /**
   * Callback when an error occurs
   */
  onError?: (error: string) => void;
}

interface UseWorkspacesReturn {
  /**
   * Array of workspaces
   */
  workspaces: Workspace[];
  
  /**
   * Current workspace
   */
  currentWorkspace: Workspace | null;
  
  /**
   * Loading state
   */
  isLoading: boolean;
  
  /**
   * Error state
   */
  error: string | null;
  
  /**
   * Manually fetch workspaces
   */
  fetchWorkspaces: () => Promise<Workspace[]>;
  
  /**
   * Refresh workspaces (force fetch)
   */
  refreshWorkspaces: () => Promise<Workspace[]>;
  
  /**
   * Set current workspace
   */
  setCurrentWorkspace: (workspace: Workspace | null) => void;
  
  /**
   * Clear error state
   */
  clearError: () => void;
}

/**
 * Hook for managing workspace data with automatic deduplication and caching
 */
export function useWorkspaces(options: UseWorkspacesOptions = {}): UseWorkspacesReturn {
  const {
    autoFetch = true,
    refetchOnAuth = true,
    onSuccess,
    onError,
  } = options;

  // Auth state
  const { isLoaded: isAuthLoaded, userId, getToken } = useAuth();
  
  // Store state
  const workspaces = useWorkspacesFromStore();
  const currentWorkspace = useWorkspaceStore((state) => state.currentWorkspace);
  const isLoading = useWorkspaceLoading();
  const error = useWorkspaceError();
  
  // Store actions
  const setCurrentWorkspace = useWorkspaceStore((state) => state.setCurrentWorkspace);
  const setError = useWorkspaceStore((state) => state.setError);
  
  // Refs to track previous values
  const prevUserIdRef = useRef<string | null>(null);
  const hasInitialFetchRef = useRef(false);
  
  // Memoized fetch function
  const fetchWorkspaces = useCallback(async (): Promise<Workspace[]> => {
    try {
      console.log('🔄 useWorkspaces: Fetching workspaces...');
      const result = await centralizedWorkspaceService.fetchWorkspaces(getToken);
      onSuccess?.(result);
      return result;
    } catch (err: any) {
      // Don't treat cancelled requests as errors
      if (err.name === 'AbortError' || err.message === 'Request was cancelled') {
        console.log('🚫 useWorkspaces: Request was cancelled, returning current workspaces');
        // Return current workspaces from store instead of throwing
        const currentWorkspaces = useWorkspaceStore.getState().workspaces;
        return currentWorkspaces;
      }

      const errorMessage = err.message || 'Failed to fetch workspaces';
      console.error('❌ useWorkspaces: Error fetching workspaces:', errorMessage);
      onError?.(errorMessage);
      throw err;
    }
  }, [getToken, onSuccess, onError]);

  // Memoized refresh function
  const refreshWorkspaces = useCallback(async (): Promise<Workspace[]> => {
    try {
      console.log('🔄 useWorkspaces: Refreshing workspaces...');
      const result = await centralizedWorkspaceService.refreshWorkspaces(getToken);
      onSuccess?.(result);
      return result;
    } catch (err: any) {
      // Don't treat cancelled requests as errors
      if (err.name === 'AbortError' || err.message === 'Request was cancelled') {
        console.log('🚫 useWorkspaces: Refresh request was cancelled, returning current workspaces');
        // Return current workspaces from store instead of throwing
        const currentWorkspaces = useWorkspaceStore.getState().workspaces;
        return currentWorkspaces;
      }

      const errorMessage = err.message || 'Failed to refresh workspaces';
      console.error('❌ useWorkspaces: Error refreshing workspaces:', errorMessage);
      onError?.(errorMessage);
      throw err;
    }
  }, [getToken, onSuccess, onError]);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  // Effect for initial fetch and auth changes
  useEffect(() => {
    // Don't do anything if auth is not loaded
    if (!isAuthLoaded) {
      console.log('⏳ useWorkspaces: Waiting for auth to load...');
      return;
    }

    // Check if user changed
    const userChanged = prevUserIdRef.current !== userId;
    prevUserIdRef.current = userId;

    // If no user, reset state
    if (!userId) {
      console.log('🚫 useWorkspaces: No user, resetting workspace state');
      centralizedWorkspaceService.cancelAllRequests();
      useWorkspaceStore.getState().reset();
      hasInitialFetchRef.current = false;
      return;
    }

    // Determine if we should fetch
    const shouldFetch = (autoFetch && !hasInitialFetchRef.current) || 
                       (refetchOnAuth && userChanged && hasInitialFetchRef.current);

    if (shouldFetch) {
      console.log('🔄 useWorkspaces: Triggering fetch due to:', {
        autoFetch: autoFetch && !hasInitialFetchRef.current,
        userChanged: refetchOnAuth && userChanged && hasInitialFetchRef.current,
      });

      fetchWorkspaces()
        .catch((err) => {
          // Silently handle cancelled requests
          if (err.name === 'AbortError' || err.message === 'Request was cancelled') {
            console.log('🚫 useWorkspaces: Fetch was cancelled, ignoring error');
            return;
          }
          // Re-throw other errors
          throw err;
        })
        .finally(() => {
          hasInitialFetchRef.current = true;
        });
    }
  }, [isAuthLoaded, userId, autoFetch, refetchOnAuth, fetchWorkspaces]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Only cancel requests if we're actually unmounting, not just re-rendering
      console.log('🧹 useWorkspaces: Component unmounting, cancelling active requests');
      centralizedWorkspaceService.cancelAllRequests();
    };
  }, []);

  return {
    workspaces,
    currentWorkspace,
    isLoading,
    error,
    fetchWorkspaces,
    refreshWorkspaces,
    setCurrentWorkspace,
    clearError,
  };
}
