import React from 'react';
import { SignUp as ClerkSignUp } from '@clerk/clerk-react';
import { LandingThemeProvider } from '@/lib/landing-theme-provider';

const SignUp: React.FC = () => {
  return (
    <LandingThemeProvider>
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <div className="w-full max-w-md">
          <div className="mb-6 text-center">
            <h1 className="text-2xl font-bold text-foreground">Averum</h1>
            <p className="text-muted-foreground">Create your account</p>
          </div>
          <ClerkSignUp
            routing="path"
            path="/sign-up"
            signInUrl="/sign-in"
          />
        </div>
      </div>
    </LandingThemeProvider>
  );
};

export default SignUp;
