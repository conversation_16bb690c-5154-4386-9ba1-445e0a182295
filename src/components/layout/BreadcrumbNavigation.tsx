import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  B<PERSON>crumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";

interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

interface BreadcrumbNavigationProps {
  className?: string;
}

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({ className }) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Generate breadcrumb items based on current route
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const path = location.pathname;
    const breadcrumbs: BreadcrumbItem[] = [];

    // Route mapping for breadcrumbs
    const routeMap: Record<string, { label: string; parent?: string }> = {
      '/app': { label: 'Home' },
      '/app/dashboard': { label: 'Dashboard' },
      '/app/contracts': { label: 'Contracts' },
      '/app/contracts/create': { label: 'Create', parent: '/app/contracts' },
      '/app/contracts/new': { label: 'New', parent: '/app/contracts' },
      '/app/contracts/import': { label: 'Import', parent: '/app/contracts' },
      '/app/contracts/templates': { label: 'Templates', parent: '/app/contracts' },
      '/app/contracts/wizard': { label: 'Wizard', parent: '/app/contracts' },
      '/app/repository': { label: 'Repository' },
      '/app/approvals': { label: 'Approvals' },
      '/app/analytics': { label: 'Analytics' },
      '/app/settings': { label: 'Settings' },
      '/app/workspaces': { label: 'Workspaces' },
      '/app/activity': { label: 'Activity' },
      '/app/clause-library': { label: 'Clause Library' },
      '/app/profile': { label: 'Profile' },
      '/app/debug': { label: 'Debug' },
    };

    // Handle dynamic routes with parameters
    const handleDynamicRoutes = (currentPath: string): BreadcrumbItem[] => {
      // Contract analysis routes
      if (currentPath.match(/^\/app\/contracts\/analysis\/(.+)$/)) {
        return [
          { label: 'Contracts', href: '/app/contracts' },
          { label: 'Analysis', isCurrentPage: true },
        ];
      }

      // Contract preview routes
      if (currentPath.match(/^\/app\/contracts\/preview\/(.+)$/)) {
        return [
          { label: 'Contracts', href: '/app/contracts' },
          { label: 'Preview', isCurrentPage: true },
        ];
      }

      // Contract versions routes
      if (currentPath.match(/^\/app\/contracts\/versions\/(.+)$/)) {
        return [
          { label: 'Contracts', href: '/app/contracts' },
          { label: 'Versions', isCurrentPage: true },
        ];
      }

      // Approval workflow routes
      if (currentPath.match(/^\/app\/approvals\/workflow\/(.+)$/)) {
        return [
          { label: 'Approvals', href: '/app/approvals' },
          { label: 'Workflow', isCurrentPage: true },
        ];
      }

      // Signature routes
      if (currentPath.match(/^\/app\/signature\/(.+)$/)) {
        return [
          { label: 'Signatures', isCurrentPage: true },
        ];
      }

      // Clause categorization
      if (currentPath === '/app/clauses/categorization') {
        return [
          { label: 'Clause Library', href: '/app/clause-library' },
          { label: 'Categorization', isCurrentPage: true },
        ];
      }

      // Clause suggestions
      if (currentPath === '/app/clauses/suggestions') {
        return [
          { label: 'Clause Library', href: '/app/clause-library' },
          { label: 'Suggestions', isCurrentPage: true },
        ];
      }

      // Settings roles
      if (currentPath === '/app/settings/roles') {
        return [
          { label: 'Settings', href: '/app/settings' },
          { label: 'Roles & Permissions', isCurrentPage: true },
        ];
      }

      return [];
    };

    // Check for dynamic routes first
    const dynamicBreadcrumbs = handleDynamicRoutes(path);
    if (dynamicBreadcrumbs.length > 0) {
      return dynamicBreadcrumbs;
    }

    // Find exact match or closest parent route
    let currentRoute = path;
    const routeInfo = routeMap[currentRoute];

    if (routeInfo) {
      // Build breadcrumb chain
      const buildChain = (route: string): void => {
        const info = routeMap[route];
        if (info) {
          if (info.parent) {
            buildChain(info.parent);
          }
          breadcrumbs.push({
            label: info.label,
            href: route === path ? undefined : route,
            isCurrentPage: route === path,
          });
        }
      };

      buildChain(currentRoute);
    } else {
      // Fallback for unknown routes
      breadcrumbs.push({
        label: 'Page',
        isCurrentPage: true,
      });
    }

    return breadcrumbs;
  };

  const breadcrumbItems = generateBreadcrumbs();

  // Don't render if no breadcrumbs
  if (breadcrumbItems.length === 0) {
    return null;
  }

  const handleNavigation = (href: string) => {
    navigate(href);
  };

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList className="flex-nowrap overflow-hidden">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const isSecondToLast = index === breadcrumbItems.length - 2;

          // On mobile: show only current page, or current + parent if only 2 items
          const showOnMobile = isLast || (isSecondToLast && breadcrumbItems.length === 2);

          return (
            <React.Fragment key={index}>
              <BreadcrumbItem className={`flex-shrink-0 ${showOnMobile ? 'flex' : 'hidden sm:flex'}`}>
                {item.isCurrentPage ? (
                  <BreadcrumbPage className="text-sm font-medium truncate max-w-[120px] sm:max-w-none">
                    {item.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink
                    className="text-sm text-muted-foreground hover:text-foreground cursor-pointer transition-colors truncate max-w-[100px] sm:max-w-none"
                    onClick={() => item.href && handleNavigation(item.href)}
                  >
                    {item.label}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {index < breadcrumbItems.length - 1 && (
                <BreadcrumbSeparator className={`flex-shrink-0 ${showOnMobile && (isLast || breadcrumbItems.length === 2) ? 'flex' : 'hidden sm:flex'}`} />
              )}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadcrumbNavigation;
