import React from 'react';
import { useOnboarding } from './OnboardingContext';
import { OnboardingFlow } from './OnboardingFlow';
import { GuidedTour, useGuidedTour, dashboardTour, contractsTour, templatesTour } from './GuidedTour';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { HelpCircle, BookOpen, Target, RotateCcw } from 'lucide-react';

interface OnboardingWrapperProps {
  children: React.ReactNode;
  userRole?: string;
  workspaceName?: string;
}

export const OnboardingWrapper: React.FC<OnboardingWrapperProps> = ({
  children,
  userRole = 'user',
  workspaceName = 'Your Workspace'
}) => {
  const { showOnboarding, completeOnboarding, startOnboarding, resetOnboarding } = useOnboarding();
  const { activeTour, isActive, startTour, completeTour, skipTour } = useGuidedTour();

  const handleStartDashboardTour = () => {
    startTour(dashboardTour);
  };

  const handleStartContractsTour = () => {
    startTour(contractsTour);
  };

  const handleStartTemplatesTour = () => {
    startTour(templatesTour);
  };

  return (
    <>
      {children}
      
      {/* Main Onboarding Flow */}
      {showOnboarding && (
        <OnboardingFlow
          onComplete={completeOnboarding}
          userRole={userRole}
          workspaceName={workspaceName}
        />
      )}
      
      {/* Guided Tours */}
      {activeTour && (
        <GuidedTour
          steps={activeTour}
          isActive={isActive}
          onComplete={completeTour}
          onSkip={skipTour}
        />
      )}
      
      {/* Help Menu - Fixed position help button */}
      <div className="fixed bottom-4 right-4 z-30">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="rounded-full shadow-lg bg-white hover:bg-gray-50"
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem onClick={startOnboarding}>
              <BookOpen className="h-4 w-4 mr-2" />
              Start Onboarding Tour
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleStartDashboardTour}>
              <Target className="h-4 w-4 mr-2" />
              Dashboard Tour
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleStartContractsTour}>
              <Target className="h-4 w-4 mr-2" />
              Contracts Tour
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleStartTemplatesTour}>
              <Target className="h-4 w-4 mr-2" />
              Templates Tour
            </DropdownMenuItem>
            <DropdownMenuItem onClick={resetOnboarding}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset Onboarding
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  );
};

// Hook to add tour data attributes to elements
export const useTourAttributes = () => {
  const addTourAttribute = (elementId: string, tourId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.setAttribute('data-tour', tourId);
    }
  };

  const removeTourAttribute = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.removeAttribute('data-tour');
    }
  };

  return { addTourAttribute, removeTourAttribute };
};

// Component to mark elements for tours
export const TourTarget: React.FC<{
  tourId: string;
  children: React.ReactNode;
  className?: string;
}> = ({ tourId, children, className = '' }) => {
  return (
    <div data-tour={tourId} className={className}>
      {children}
    </div>
  );
};

// CSS for tour highlighting (add to your global CSS)
export const tourStyles = `
.tour-highlight {
  position: relative;
  z-index: 41;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
  border-radius: 4px;
  transition: box-shadow 0.3s ease;
}

.tour-highlight::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  z-index: -1;
}
`;

// Example usage component
export const OnboardingExample: React.FC = () => {
  return (
    <div className="p-6 space-y-4">
      <h1>Averum Contracts Dashboard</h1>
      
      {/* Dashboard stats with tour target */}
      <TourTarget tourId="dashboard-stats" className="grid grid-cols-3 gap-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3>Total Contracts</h3>
          <p className="text-2xl font-bold">24</p>
        </div>
        <div className="p-4 bg-green-50 rounded-lg">
          <h3>Active Contracts</h3>
          <p className="text-2xl font-bold">18</p>
        </div>
        <div className="p-4 bg-yellow-50 rounded-lg">
          <h3>Pending Approval</h3>
          <p className="text-2xl font-bold">3</p>
        </div>
      </TourTarget>
      
      {/* Contracts list with tour target */}
      <TourTarget tourId="contracts-list">
        <div className="bg-white rounded-lg border p-4">
          <h2>Recent Contracts</h2>
          <div className="space-y-2 mt-4">
            <div className="p-3 border rounded">Contract 1</div>
            <div className="p-3 border rounded">Contract 2</div>
            <div className="p-3 border rounded">Contract 3</div>
          </div>
        </div>
      </TourTarget>
      
      {/* Create contract button with tour target */}
      <TourTarget tourId="create-contract-btn">
        <Button className="w-full">
          Create New Contract
        </Button>
      </TourTarget>
    </div>
  );
};
