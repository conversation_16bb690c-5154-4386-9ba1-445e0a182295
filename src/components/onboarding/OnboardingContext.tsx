import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface OnboardingContextType {
  isOnboardingComplete: boolean;
  showOnboarding: boolean;
  completeOnboarding: () => void;
  startOnboarding: () => void;
  skipOnboarding: () => void;
  resetOnboarding: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

interface OnboardingProviderProps {
  children: ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({ children }) => {
  const [isOnboardingComplete, setIsOnboardingComplete] = useState<boolean>(false);
  const [showOnboarding, setShowOnboarding] = useState<boolean>(false);

  // Check if user has completed onboarding on mount
  useEffect(() => {
    const onboardingStatus = localStorage.getItem('averum_onboarding_complete');
    const isComplete = onboardingStatus === 'true';
    setIsOnboardingComplete(isComplete);
    
    // Show onboarding for new users
    if (!isComplete) {
      setShowOnboarding(true);
    }
  }, []);

  const completeOnboarding = () => {
    setIsOnboardingComplete(true);
    setShowOnboarding(false);
    localStorage.setItem('averum_onboarding_complete', 'true');
    localStorage.setItem('averum_onboarding_completed_at', new Date().toISOString());
  };

  const startOnboarding = () => {
    setShowOnboarding(true);
  };

  const skipOnboarding = () => {
    setIsOnboardingComplete(true);
    setShowOnboarding(false);
    localStorage.setItem('averum_onboarding_complete', 'true');
    localStorage.setItem('averum_onboarding_skipped', 'true');
    localStorage.setItem('averum_onboarding_completed_at', new Date().toISOString());
  };

  const resetOnboarding = () => {
    setIsOnboardingComplete(false);
    setShowOnboarding(true);
    localStorage.removeItem('averum_onboarding_complete');
    localStorage.removeItem('averum_onboarding_skipped');
    localStorage.removeItem('averum_onboarding_completed_at');
  };

  const value: OnboardingContextType = {
    isOnboardingComplete,
    showOnboarding,
    completeOnboarding,
    startOnboarding,
    skipOnboarding,
    resetOnboarding,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = (): OnboardingContextType => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};

// Hook to check if user is a new user (for conditional onboarding)
export const useIsNewUser = (): boolean => {
  const [isNewUser, setIsNewUser] = useState<boolean>(false);

  useEffect(() => {
    // Check various indicators of a new user
    const onboardingComplete = localStorage.getItem('averum_onboarding_complete');
    const userCreatedAt = localStorage.getItem('user_created_at');
    const hasCreatedContract = localStorage.getItem('has_created_contract');
    
    // Consider user "new" if they haven't completed onboarding
    // and haven't created any contracts
    const isNew = !onboardingComplete && !hasCreatedContract;
    setIsNewUser(isNew);
  }, []);

  return isNewUser;
};

// Hook for onboarding analytics
export const useOnboardingAnalytics = () => {
  const trackOnboardingStep = (stepId: string, stepNumber: number) => {
    // Track onboarding progress for analytics
    const event = {
      type: 'onboarding_step_viewed',
      stepId,
      stepNumber,
      timestamp: new Date().toISOString(),
    };
    
    // Store in localStorage for now (could be sent to analytics service)
    const existingEvents = JSON.parse(localStorage.getItem('onboarding_events') || '[]');
    existingEvents.push(event);
    localStorage.setItem('onboarding_events', JSON.stringify(existingEvents));
  };

  const trackOnboardingCompletion = (method: 'completed' | 'skipped') => {
    const event = {
      type: 'onboarding_finished',
      method,
      timestamp: new Date().toISOString(),
    };
    
    const existingEvents = JSON.parse(localStorage.getItem('onboarding_events') || '[]');
    existingEvents.push(event);
    localStorage.setItem('onboarding_events', JSON.stringify(existingEvents));
  };

  return {
    trackOnboardingStep,
    trackOnboardingCompletion,
  };
};
