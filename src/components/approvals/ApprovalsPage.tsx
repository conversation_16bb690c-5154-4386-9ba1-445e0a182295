import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  ThumbsUp,
  Eye,
  AlertCircle,
  ArrowLeft,
} from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import ApprovalWorkflow, { Approver } from "./ApprovalWorkflow";
import { DocumentPreviewModal } from "@/engines/document-engine/preview/DocumentPreviewModal";
import { useDocumentPreview } from "@/engines/document-engine/hooks/useDocumentPreview";
import CompactSearch from "@/components/ui/compact-search";
import { useSearchAndFilter } from "@/hooks/useSearchAndFilter";

const ApprovalsPage = () => {
  const [activeTab, setActiveTab] = useState("pending");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedApproval, setSelectedApproval] = useState<string | null>(null);
  const [showWorkflow, setShowWorkflow] = useState<boolean>(false);

  // Document preview hook
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  // Mock data for approvals
  const pendingApprovals = [
    {
      id: "a1",
      title: "Service Agreement with Acme Corp",
      type: "Service Agreement",
      submittedBy: {
        name: "Jane Smith",
        initials: "JS",
      },
      submittedDate: "2023-06-15",
      dueDate: "2023-06-22",
      priority: "high",
      approvers: [
        { name: "Legal Department", status: "approved" },
        { name: "Finance Department", status: "pending" },
        { name: "Executive Team", status: "pending" },
      ],
    },
    {
      id: "a2",
      title: "Non-Disclosure Agreement with TechStart",
      type: "NDA",
      submittedBy: {
        name: "John Doe",
        initials: "JD",
      },
      submittedDate: "2023-06-10",
      dueDate: "2023-06-20",
      priority: "medium",
      approvers: [{ name: "Legal Department", status: "pending" }],
    },
    {
      id: "a3",
      title: "Employment Contract - Senior Developer",
      type: "Employment",
      submittedBy: {
        name: "Sarah Johnson",
        initials: "SJ",
      },
      submittedDate: "2023-06-12",
      dueDate: "2023-06-19",
      priority: "medium",
      approvers: [
        { name: "HR Department", status: "approved" },
        { name: "Legal Department", status: "approved" },
        { name: "Department Head", status: "pending" },
      ],
    },
  ];

  const completedApprovals = [
    {
      id: "a4",
      title: "Software License Agreement",
      type: "License",
      submittedBy: {
        name: "Michael Chen",
        initials: "MC",
      },
      submittedDate: "2023-05-28",
      completedDate: "2023-06-05",
      status: "approved",
      approvers: [
        { name: "Legal Department", status: "approved" },
        { name: "Finance Department", status: "approved" },
        { name: "IT Department", status: "approved" },
      ],
    },
    {
      id: "a5",
      title: "Office Lease Agreement",
      type: "Lease",
      submittedBy: {
        name: "David Wilson",
        initials: "DW",
      },
      submittedDate: "2023-05-20",
      completedDate: "2023-06-02",
      status: "rejected",
      approvers: [
        { name: "Legal Department", status: "approved" },
        { name: "Finance Department", status: "rejected" },
      ],
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDaysRemaining = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge variant="destructive">High Priority</Badge>;
      case "medium":
        return <Badge variant="secondary">Medium Priority</Badge>;
      case "low":
        return <Badge variant="outline">Low Priority</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Approved
          </Badge>
        );
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "pending":
        return <Badge variant="outline">Pending</Badge>;
      default:
        return null;
    }
  };

  // Search and filter functionality
  const allApprovals = [...pendingApprovals, ...completedApprovals];
  const searchAndFilter = useSearchAndFilter({
    data: allApprovals,
    searchFields: ['title', 'type', 'submittedBy.name'],
    defaultSearch: { global: '', fields: {} },
  });

  const filteredPendingApprovals = searchAndFilter.filteredData.filter(
    (approval) => 'priority' in approval // Only pending approvals have priority
  );

  const filteredCompletedApprovals = searchAndFilter.filteredData.filter(
    (approval) => 'status' in approval // Only completed approvals have status
  );

  const handleApprove = (id: string) => {
    console.log(`Approving document with ID: ${id}`);
  };

  const handleReject = (id: string) => {
    console.log(`Rejecting document with ID: ${id}`);
  };

  const handleViewDetails = (id: string) => {
    setSelectedApproval(id);
    setShowWorkflow(true);
  };

  const handleBackToList = () => {
    setSelectedApproval(null);
    setShowWorkflow(false);
  };

  // Handle opening the document preview modal
  const handleViewContract = (contractId: string) => {
    const contract = [...pendingApprovals, ...completedApprovals].find(a => a.id === contractId);
    if (contract) {
      // Sample contract content for preview
      const sampleContent = `
        <h1>${contract.title}</h1>
        <p><strong>Contract Type:</strong> ${contract.type}</p>
        <p><strong>Submitted by:</strong> ${contract.submittedBy.name}</p>
        <h2>Terms and Conditions</h2>
        <p>This is a sample contract document for approval review purposes.</p>
        <p>The contract contains standard terms and conditions that need to be reviewed and approved.</p>
      `;

      openPreview(sampleContent, contract.title);
    }
  };

  // Handle reviewing a contract (view with approval actions)
  const handleReviewContract = (contractId: string) => {
    handleViewContract(contractId);
  };

  // Mock data for approval workflow
  const mockApprovers: Approver[] = [
    {
      id: "approver-1",
      name: "John Smith",
      role: "Legal Director",
      status: "approved",
      order: 1,
      completedAt: "2023-07-10T14:30:00Z",
      comments: "Looks good to me. All legal requirements are met.",
    },
    {
      id: "approver-2",
      name: "Jane Doe",
      role: "Finance Manager",
      status: "current",
      order: 2,
    },
    {
      id: "approver-3",
      name: "Alice Johnson",
      role: "Operations Director",
      status: "pending",
      order: 3,
    },
  ];

  // Render pending approvals in grid view
  const renderPendingApprovalsGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {filteredPendingApprovals.map((approval) => (
        <Card key={approval.id} className="h-full flex flex-col">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-sm font-medium">{approval.title}</CardTitle>
                <CardDescription className="text-xs">{approval.type}</CardDescription>
              </div>
              {getPriorityBadge(approval.priority)}
            </div>
          </CardHeader>
          <CardContent className="flex-grow flex flex-col">
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
                  {approval.submittedBy.initials}
                </div>
                <span className="text-sm text-muted-foreground">
                  Submitted by {approval.submittedBy.name}
                </span>
              </div>

              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Submitted: {formatDate(approval.submittedDate)}</span>
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {getDaysRemaining(approval.dueDate)} days left
                </span>
              </div>
            </div>

            <div className="space-y-2 mb-auto">
              <div className="text-sm font-medium">Approval Status:</div>
              {approval.approvers.map((approver, idx) => (
                <div key={idx} className="flex justify-between items-center">
                  <span className="text-sm">{approver.name}</span>
                  {getStatusBadge(approver.status)}
                </div>
              ))}
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => handleReviewContract(approval.id)}
              >
                <Eye className="mr-2 h-4 w-4" />
                Review
              </Button>
              <Button
                variant="default"
                className="flex-1"
                onClick={() => handleApprove(approval.id)}
              >
                <ThumbsUp className="mr-2 h-4 w-4" />
                Approve
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {filteredPendingApprovals.length === 0 && (
        <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
          <CheckCircle className="h-12 w-12 text-muted-foreground mb-3" />
          <h3 className="text-lg font-medium mb-1">No pending approvals</h3>
          <p className="text-sm text-muted-foreground">
            There are no contracts waiting for your approval.
          </p>
        </div>
      )}
    </div>
  );

  // Render completed approvals in grid view
  const renderCompletedApprovalsGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {filteredCompletedApprovals.map((approval) => (
        <Card key={approval.id} className="h-full flex flex-col">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-sm font-medium">{approval.title}</CardTitle>
                <CardDescription className="text-xs">{approval.type}</CardDescription>
              </div>
              {getStatusBadge(approval.status)}
            </div>
          </CardHeader>
          <CardContent className="flex-grow flex flex-col">
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
                  {approval.submittedBy.initials}
                </div>
                <span className="text-sm text-muted-foreground">
                  Submitted by {approval.submittedBy.name}
                </span>
              </div>

              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Submitted: {formatDate(approval.submittedDate)}</span>
                <span>Completed: {formatDate(approval.completedDate)}</span>
              </div>
            </div>

            <div className="space-y-2 mb-auto">
              <div className="text-sm font-medium">Approval Status:</div>
              {approval.approvers.map((approver, idx) => (
                <div key={idx} className="flex justify-between items-center">
                  <span className="text-sm">{approver.name}</span>
                  {getStatusBadge(approver.status)}
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={() => handleViewDetails(approval.id)}
            >
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </Button>
          </CardContent>
        </Card>
      ))}

      {filteredCompletedApprovals.length === 0 && (
        <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-3" />
          <h3 className="text-lg font-medium mb-1">No completed approvals</h3>
          <p className="text-sm text-muted-foreground">
            You haven&apos;t completed any approval requests yet.
          </p>
        </div>
      )}
    </div>
  );

  // Render pending approvals in table/list view
  const renderPendingApprovalsList = () => (
    <Card>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Contract</TableHead>
            <TableHead>Submitted By</TableHead>
            <TableHead>Submitted Date</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredPendingApprovals.map((approval) => (
            <TableRow key={approval.id}>
              <TableCell>
                <div className="font-medium">{approval.title}</div>
                <div className="text-sm text-muted-foreground">{approval.type}</div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
                    {approval.submittedBy.initials}
                  </div>
                  <span>{approval.submittedBy.name}</span>
                </div>
              </TableCell>
              <TableCell>{formatDate(approval.submittedDate)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  {formatDate(approval.dueDate)}
                  <span className="text-xs text-muted-foreground ml-1">
                    ({getDaysRemaining(approval.dueDate)} days left)
                  </span>
                </div>
              </TableCell>
              <TableCell>{getPriorityBadge(approval.priority)}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  {approval.approvers.map((approver, idx) => (
                    <div key={idx} className="flex items-center gap-1 text-xs">
                      <span className="text-muted-foreground">{approver.name}:</span>
                      {getStatusBadge(approver.status)}
                    </div>
                  ))}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleReviewContract(approval.id)}
                  >
                    <Eye className="mr-2 h-3 w-3" />
                    Review
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleApprove(approval.id)}
                  >
                    <ThumbsUp className="mr-2 h-3 w-3" />
                    Approve
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}

          {filteredPendingApprovals.length === 0 && (
            <TableRow>
              <TableCell colSpan={7} className="h-32 text-center">
                <div className="flex flex-col items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No pending approvals</p>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </Card>
  );

  // Render completed approvals in table/list view
  const renderCompletedApprovalsList = () => (
    <Card>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Contract</TableHead>
            <TableHead>Submitted By</TableHead>
            <TableHead>Completed Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredCompletedApprovals.map((approval) => (
            <TableRow key={approval.id}>
              <TableCell>
                <div className="font-medium">{approval.title}</div>
                <div className="text-sm text-muted-foreground">{approval.type}</div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
                    {approval.submittedBy.initials}
                  </div>
                  <span>{approval.submittedBy.name}</span>
                </div>
              </TableCell>
              <TableCell>{formatDate(approval.completedDate)}</TableCell>
              <TableCell>{getStatusBadge(approval.status)}</TableCell>
              <TableCell className="text-right">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewDetails(approval.id)}
                >
                  <Eye className="mr-2 h-3 w-3" />
                  View Details
                </Button>
              </TableCell>
            </TableRow>
          ))}

          {filteredCompletedApprovals.length === 0 && (
            <TableRow>
              <TableCell colSpan={5} className="h-32 text-center">
                <div className="flex flex-col items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No completed approvals</p>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </Card>
  );

  return (
    <div className="w-full h-full bg-background overflow-auto">
      {!showWorkflow ? (
        <div className="page-container">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="page-header">
              {/* Left side: Tabs */}
              <TabsList className="grid grid-cols-2">
                <TabsTrigger value="pending">Pending Approvals</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
              </TabsList>

              {/* Right side: Search and actions */}
              <div className="flex items-center gap-2">
                <CompactSearch
                  searchValue={searchAndFilter.state.search}
                  onSearchChange={searchAndFilter.actions.setSearch}
                  searchPlaceholder="Search approvals..."
                  recentSearches={searchAndFilter.state.recentSearches}
                  suggestions={searchAndFilter.getSearchSuggestions()}
                  totalResults={searchAndFilter.filteredData.length}
                  showResultsCount={true}
                />
              </div>
            </div>

        <TabsContent value="pending" className="mt-0">
          {viewMode === "grid" ? renderPendingApprovalsGrid() : renderPendingApprovalsList()}
        </TabsContent>

        <TabsContent value="completed" className="mt-0">
          {viewMode === "grid" ? renderCompletedApprovalsGrid() : renderCompletedApprovalsList()}
        </TabsContent>
      </Tabs>
        </div>
      ) : (
        <div className="w-full">
          <div className="flex items-center mb-4">
            <Button variant="ghost" size="sm" onClick={handleBackToList} className="mr-3 h-8">
              <ArrowLeft className="h-3 w-3 mr-2" />
              Back to Approvals
            </Button>
          </div>

          <ApprovalWorkflow
            contractId={selectedApproval || "contract-123"}
            contractTitle={
              [...pendingApprovals, ...completedApprovals].find(a => a.id === selectedApproval)?.title ||
              "Contract Approval"
            }
            approvers={mockApprovers}
            approvalProcess="sequential"
            dueDate="2023-07-20T23:59:59Z"
            onApprove={(contractId, comments) => {
              console.log("Approved contract:", contractId, "with comments:", comments);
              handleBackToList();
            }}
            onReject={(contractId, reason) => {
              console.log("Rejected contract:", contractId, "with reason:", reason);
              handleBackToList();
            }}
            onAddApprover={(approver) => {
              console.log("Added approver:", approver);
            }}
            onViewContract={handleViewContract}
          />
        </div>
      )}

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true,
          documentTitle: previewState.title
        }}
      />
    </div>
  );
};

export default ApprovalsPage;
