import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  Play, 
  FileText, 
  Users, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp, 
  Sparkles,
  Plus,
  Search,
  Filter,
  Download,
  MessageSquare,
  Clock,
  BarChart3,
  PieChart,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { useState } from 'react';

const UserJourneyStory = () => {
  const [activeStep, setActiveStep] = useState(0);
  
  const steps = [
    {
      id: 'create',
      title: 'Start with Smart Templates',
      subtitle: 'Choose from 100+ industry-specific contract templates',
      description: 'Begin your contract creation journey with our intelligent template library. Our AI suggests the best template based on your industry and contract type.'
    },
    {
      id: 'analyze',
      title: 'AI Reviews in Seconds',
      subtitle: 'Advanced AI analysis identifies risks and compliance issues',
      description: 'Our AI engine analyzes your contract in real-time, highlighting potential risks, compliance issues, and optimization opportunities.'
    },
    {
      id: 'collaborate',
      title: 'Collaborate Seamlessly',
      subtitle: 'Team review and approval workflows made simple',
      description: 'Invite team members to review, comment, and approve contracts with role-based permissions and automated workflows.'
    },
    {
      id: 'manage',
      title: 'Organize & Track Everything',
      subtitle: 'Centralized document management with powerful search',
      description: 'Store, organize, and track all your contracts in one secure location with advanced search and filtering capabilities.'
    },
    {
      id: 'insights',
      title: 'Gain Powerful Insights',
      subtitle: 'Analytics dashboard reveals contract performance',
      description: 'Monitor contract performance, track key metrics, and gain insights to optimize your legal operations.'
    }
  ];

  return (
    <section className="py-32 bg-gradient-to-b from-background to-muted/20 relative">
      {/* Background decoration */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
      
      <div className="relative px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
              <Sparkles className="w-4 h-4" />
              Complete Workflow
            </div>
            <h2 className="text-4xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              See how Averum Contracts transforms your workflow
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Follow a complete contract journey from creation to insights. See how our platform streamlines every step of your legal workflow.
            </p>
          </div>

          {/* Progress Steps */}
          <div className="flex justify-center mb-16">
            <div className="flex items-center gap-4 p-2 bg-muted/50 rounded-2xl">
              {steps.map((step, index) => (
                <button
                  key={step.id}
                  onClick={() => setActiveStep(index)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                    activeStep === index 
                      ? 'bg-primary text-primary-foreground shadow-lg' 
                      : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <span className="text-sm font-medium">Step {index + 1}</span>
                  {activeStep === index && <ArrowRight className="w-4 h-4" />}
                </button>
              ))}
            </div>
          </div>

          {/* Current Step Info */}
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              {steps[activeStep].title}
            </h3>
            <p className="text-xl text-primary font-semibold mb-2">
              {steps[activeStep].subtitle}
            </p>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {steps[activeStep].description}
            </p>
          </div>

          {/* Mockup Display */}
          <div className="relative">
            {activeStep === 0 && <ContractCreationMockup />}
            {activeStep === 1 && <AIAnalysisMockup />}
            {activeStep === 2 && <CollaborationMockup />}
            {activeStep === 3 && <DocumentManagementMockup />}
            {activeStep === 4 && <AnalyticsMockup />}
          </div>

          {/* Navigation */}
          <div className="flex justify-center gap-4 mt-12">
            <Button
              variant="outline"
              onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
              disabled={activeStep === 0}
              className="px-6"
            >
              Previous Step
            </Button>
            <Button
              onClick={() => setActiveStep(Math.min(steps.length - 1, activeStep + 1))}
              disabled={activeStep === steps.length - 1}
              className="px-6"
            >
              Next Step
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

// Contract Creation Mockup Component
const ContractCreationMockup = () => {
  return (
    <div className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto">
      {/* Browser header */}
      <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-background rounded-lg px-4 py-2 text-sm text-muted-foreground border shadow-sm">
            <span className="text-green-600">🔒</span> https://app.averumcontracts.com/contracts/new
          </div>
        </div>
      </div>

      {/* App Navigation */}
      <div className="flex items-center gap-6 px-6 py-4 border-b bg-gradient-to-r from-muted/10 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="font-bold text-lg">Averum</span>
        </div>
        <nav className="flex items-center gap-8 text-sm">
          <span className="text-primary font-semibold border-b-2 border-primary pb-2 px-1">
            Create Contract
          </span>
          <span className="text-muted-foreground">Templates</span>
          <span className="text-muted-foreground">Repository</span>
          <span className="text-muted-foreground">Analytics</span>
        </nav>
      </div>

      <div className="p-8">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Left: Template Selection */}
          <div>
            <h3 className="text-2xl font-bold mb-6">Choose a Template</h3>
            
            <div className="space-y-4">
              <div className="p-4 border-2 border-primary bg-primary/5 rounded-xl">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                    <FileText className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Service Agreement</h4>
                    <p className="text-sm text-muted-foreground">Professional services contract</p>
                  </div>
                  <CheckCircle className="w-5 h-5 text-primary ml-auto" />
                </div>
                <div className="text-xs text-primary bg-primary/10 px-2 py-1 rounded">
                  ✨ AI Recommended for your industry
                </div>
              </div>

              <div className="p-4 border rounded-xl hover:border-primary/50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                    <FileText className="w-5 h-5 text-muted-foreground" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Employment Contract</h4>
                    <p className="text-sm text-muted-foreground">Standard employment agreement</p>
                  </div>
                </div>
              </div>

              <div className="p-4 border rounded-xl hover:border-primary/50 transition-colors cursor-pointer">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                    <FileText className="w-5 h-5 text-muted-foreground" />
                  </div>
                  <div>
                    <h4 className="font-semibold">NDA Template</h4>
                    <p className="text-sm text-muted-foreground">Non-disclosure agreement</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right: Contract Details Form */}
          <div>
            <h3 className="text-2xl font-bold mb-6">Contract Details</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Contract Title</label>
                <input 
                  type="text" 
                  value="Service Agreement - TechCorp Inc."
                  className="w-full p-3 border rounded-lg bg-background"
                  readOnly
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Client</label>
                  <input 
                    type="text" 
                    value="TechCorp Inc."
                    className="w-full p-3 border rounded-lg bg-background"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Value</label>
                  <input 
                    type="text" 
                    value="$50,000"
                    className="w-full p-3 border rounded-lg bg-background"
                    readOnly
                  />
                </div>
              </div>

              <div>
                <Label className="block text-sm font-medium mb-2">Project Description</Label>
                <Textarea
                  value="Consulting services for digital transformation project including system analysis, implementation planning, and staff training."
                  className="h-24 resize-none"
                  readOnly
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="block text-sm font-medium mb-2">Start Date</Label>
                  <Input
                    type="date"
                    value="2024-02-01"
                    readOnly
                  />
                </div>
                <div>
                  <Label className="block text-sm font-medium mb-2">Duration</Label>
                  <Select defaultValue="6-months" disabled>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="6-months">6 months</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button className="w-full mt-6">
                <Sparkles className="w-4 h-4 mr-2" />
                Generate Contract with AI
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// AI Analysis Mockup Component
const AIAnalysisMockup = () => {
  return (
    <div className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto">
      {/* Browser header */}
      <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-background rounded-lg px-4 py-2 text-sm text-muted-foreground border shadow-sm">
            <span className="text-green-600">🔒</span> https://app.averumcontracts.com/contracts/analysis/SA-2024-001
          </div>
        </div>
      </div>

      {/* App Navigation */}
      <div className="flex items-center gap-6 px-6 py-4 border-b bg-gradient-to-r from-muted/10 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="font-bold text-lg">Averum</span>
        </div>
        <nav className="flex items-center gap-8 text-sm">
          <span className="text-muted-foreground">Create</span>
          <span className="text-primary font-semibold border-b-2 border-primary pb-2 px-1">
            AI Analysis
          </span>
          <span className="text-muted-foreground">Repository</span>
          <span className="text-muted-foreground">Analytics</span>
        </nav>
      </div>

      <div className="p-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left: Contract Content */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold mb-1">Service Agreement Analysis</h3>
                <p className="text-muted-foreground">Contract_SA_2024_001.pdf • Analysis complete</p>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-sm bg-muted text-foreground px-3 py-1 rounded-full font-medium border border-border">
                  <CheckCircle className="w-3 h-3 inline mr-1" />
                  Analysis Complete
                </span>
              </div>
            </div>

            <div className="space-y-6">
              <div className="group p-6 bg-gradient-to-r from-green-50 to-green-25 border border-green-200 rounded-xl">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  </div>
                  <span className="font-bold text-lg">Payment Terms</span>
                  <span className="text-xs bg-green-200 text-green-800 px-3 py-1 rounded-full font-medium">
                    Low Risk
                  </span>
                </div>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  "Compensation shall be $50,000 payable in monthly installments of $10,000 due on the first of each month..."
                </p>
                <div className="text-sm text-green-700 bg-green-100 p-3 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="font-medium">AI Analysis:</span>
                  </div>
                  <p className="mt-1">Payment terms are clearly defined and legally compliant. Standard industry practice.</p>
                </div>
              </div>

              <div className="group p-6 bg-gradient-to-r from-yellow-50 to-yellow-25 border border-yellow-200 rounded-xl">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  </div>
                  <span className="font-bold text-lg">Scope of Work</span>
                  <span className="text-xs bg-yellow-200 text-yellow-800 px-3 py-1 rounded-full font-medium">
                    Medium Risk
                  </span>
                </div>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  "The service provider agrees to deliver consulting services as outlined in the attached statement of work..."
                </p>
                <div className="text-sm text-yellow-700 bg-yellow-100 p-3 rounded-lg border border-yellow-200">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="font-medium">AI Recommendation:</span>
                  </div>
                  <p className="mt-1">Consider adding specific deliverables and timelines. Recommend milestone-based payments.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right: AI Dashboard */}
          <div className="space-y-8">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-2xl font-bold">AI Analysis</h3>
              </div>

              {/* Overall Score */}
              <div className="text-center p-8 bg-gradient-to-br from-primary/15 via-primary/10 to-primary/5 rounded-2xl border border-primary/20 mb-8">
                <div className="relative">
                  <div className="text-5xl font-bold text-primary mb-3">92%</div>
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <p className="text-lg font-semibold text-primary mb-1">Compliance Score</p>
                <p className="text-sm text-muted-foreground">Excellent rating</p>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center p-5 bg-gradient-to-br from-green-50 to-green-25 rounded-xl border border-green-200">
                  <div className="text-2xl font-bold mb-2 text-green-600">Low</div>
                  <p className="text-sm text-muted-foreground font-medium">Risk Level</p>
                </div>
                <div className="text-center p-5 bg-gradient-to-br from-blue-50 to-blue-25 rounded-xl border border-blue-200">
                  <div className="text-2xl font-bold mb-2 text-blue-600">2.3s</div>
                  <p className="text-sm text-muted-foreground font-medium">Analysis Time</p>
                </div>
              </div>

              {/* AI Suggestions */}
              <div>
                <h4 className="text-lg font-bold mb-4">AI Suggestions</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm font-medium text-blue-900 mb-1">Add Milestone Payments</p>
                    <p className="text-xs text-blue-700">Reduce payment risk with milestone-based structure</p>
                  </div>
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-900 mb-1">Termination Clause</p>
                    <p className="text-xs text-green-700">Well-defined termination conditions</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Collaboration Mockup Component
const CollaborationMockup = () => {
  return (
    <div className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto">
      {/* Browser header */}
      <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-background rounded-lg px-4 py-2 text-sm text-muted-foreground border shadow-sm">
            <span className="text-green-600">🔒</span> https://app.averumcontracts.com/contracts/review/SA-2024-001
          </div>
        </div>
      </div>

      {/* App Navigation */}
      <div className="flex items-center gap-6 px-6 py-4 border-b bg-gradient-to-r from-muted/10 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="font-bold text-lg">Averum</span>
        </div>
        <nav className="flex items-center gap-8 text-sm">
          <span className="text-muted-foreground">Create</span>
          <span className="text-muted-foreground">Analysis</span>
          <span className="text-primary font-semibold border-b-2 border-primary pb-2 px-1">
            Team Review
          </span>
          <span className="text-muted-foreground">Repository</span>
        </nav>
      </div>

      <div className="p-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left: Document with Comments */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold mb-1">Service Agreement Review</h3>
                <p className="text-muted-foreground">3 reviewers • 2 pending approvals</p>
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Add Comment
                </Button>
                <Button size="sm">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Approve
                </Button>
              </div>
            </div>

            <div className="space-y-6">
              {/* Contract Section with Comment */}
              <div className="relative p-6 border rounded-xl bg-muted/20">
                <div className="absolute -right-2 -top-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-3 h-3 text-white" />
                  </div>
                </div>
                <h4 className="font-semibold mb-3">Payment Terms</h4>
                <p className="text-muted-foreground mb-4">
                  "Compensation shall be $50,000 payable in monthly installments of $10,000 due on the first of each month. Late payments will incur a 1.5% monthly penalty."
                </p>

                {/* Comment Thread */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-blue-700">SJ</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">Sarah Johnson</span>
                        <span className="text-xs text-muted-foreground">Legal Director</span>
                        <span className="text-xs text-muted-foreground">2 hours ago</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        The penalty rate seems high. Industry standard is usually 1.0%. Should we adjust this?
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Button size="sm" variant="ghost" className="text-xs h-6">
                          Reply
                        </Button>
                        <Button size="sm" variant="ghost" className="text-xs h-6">
                          Resolve
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Another Section */}
              <div className="p-6 border rounded-xl">
                <h4 className="font-semibold mb-3">Termination Clause</h4>
                <p className="text-muted-foreground">
                  "Either party may terminate this agreement with 30 days written notice. Upon termination, all outstanding payments become due within 15 days."
                </p>
                <div className="flex items-center gap-2 mt-3">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600 font-medium">Approved by Legal Team</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right: Review Panel */}
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-bold mb-4">Review Status</h3>

              {/* Progress */}
              <div className="mb-6">
                <div className="flex justify-between text-sm mb-2">
                  <span>Review Progress</span>
                  <span>2/3 Approved</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-primary h-2 rounded-full" style={{width: '66%'}}></div>
                </div>
              </div>

              {/* Reviewers */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-green-700">MR</span>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">Michael Rodriguez</p>
                    <p className="text-xs text-muted-foreground">Senior Counsel</p>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>

                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-green-700">EC</span>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">Emily Chen</p>
                    <p className="text-xs text-muted-foreground">Chief Legal Officer</p>
                  </div>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>

                <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-yellow-700">SJ</span>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">Sarah Johnson</p>
                    <p className="text-xs text-muted-foreground">Legal Director</p>
                  </div>
                  <Clock className="w-5 h-5 text-yellow-600" />
                </div>
              </div>
            </div>

            {/* Activity Feed */}
            <div>
              <h4 className="font-semibold mb-4">Recent Activity</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm">Sarah added a comment on Payment Terms</p>
                    <p className="text-xs text-muted-foreground">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm">Emily approved the contract</p>
                    <p className="text-xs text-muted-foreground">4 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm">Michael approved the contract</p>
                    <p className="text-xs text-muted-foreground">6 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Document Management Mockup Component
const DocumentManagementMockup = () => {
  return (
    <div className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto">
      {/* Browser header */}
      <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-background rounded-lg px-4 py-2 text-sm text-muted-foreground border shadow-sm">
            <span className="text-green-600">🔒</span> https://app.averumcontracts.com/repository
          </div>
        </div>
      </div>

      {/* App Navigation */}
      <div className="flex items-center gap-6 px-6 py-4 border-b bg-gradient-to-r from-muted/10 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="font-bold text-lg">Averum</span>
        </div>
        <nav className="flex items-center gap-8 text-sm">
          <span className="text-muted-foreground">Create</span>
          <span className="text-muted-foreground">Analysis</span>
          <span className="text-muted-foreground">Review</span>
          <span className="text-primary font-semibold border-b-2 border-primary pb-2 px-1">
            Repository
          </span>
        </nav>
      </div>

      <div className="p-8">
        {/* Header with Search and Filters */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h3 className="text-2xl font-bold mb-1">Contract Repository</h3>
            <p className="text-muted-foreground">248 contracts • 12 pending signatures</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search contracts..."
                className="pl-10 pr-4 py-2 border rounded-lg bg-background w-64"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              New Contract
            </Button>
          </div>
        </div>

        {/* Filters Bar */}
        <div className="flex items-center gap-4 mb-6">
          <span className="text-sm font-medium">Quick Filters:</span>
          <div className="flex items-center gap-2">
            <span className="px-3 py-1 bg-primary text-primary-foreground text-xs rounded-full">
              Active (156)
            </span>
            <span className="px-3 py-1 bg-muted text-muted-foreground text-xs rounded-full hover:bg-muted/80 cursor-pointer">
              Pending (12)
            </span>
            <span className="px-3 py-1 bg-muted text-muted-foreground text-xs rounded-full hover:bg-muted/80 cursor-pointer">
              Expired (8)
            </span>
            <span className="px-3 py-1 bg-muted text-muted-foreground text-xs rounded-full hover:bg-muted/80 cursor-pointer">
              Templates (72)
            </span>
          </div>
        </div>

        {/* Contract List */}
        <div className="space-y-4">
          {/* Contract Item 1 */}
          <div className="flex items-center gap-4 p-4 border rounded-xl hover:bg-muted/20 transition-colors">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-green-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-1">
                <h4 className="font-semibold">Service Agreement - TechCorp Inc.</h4>
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">Active</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">High Value</span>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span>Client: TechCorp Inc.</span>
                <span>Value: $50,000</span>
                <span>Expires: Dec 31, 2024</span>
                <span>Last modified: 2 hours ago</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Download className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                View
              </Button>
            </div>
          </div>

          {/* Contract Item 2 */}
          <div className="flex items-center gap-4 p-4 border rounded-xl hover:bg-muted/20 transition-colors">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-yellow-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-1">
                <h4 className="font-semibold">Employment Contract - John Smith</h4>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">Pending Signature</span>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span>Employee: John Smith</span>
                <span>Position: Senior Developer</span>
                <span>Start Date: Feb 15, 2024</span>
                <span>Last modified: 1 day ago</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Download className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                View
              </Button>
            </div>
          </div>

          {/* Contract Item 3 */}
          <div className="flex items-center gap-4 p-4 border rounded-xl hover:bg-muted/20 transition-colors">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-1">
                <h4 className="font-semibold">NDA - DataCorp Partnership</h4>
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">Signed</span>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span>Partner: DataCorp Ltd.</span>
                <span>Type: Mutual NDA</span>
                <span>Signed: Jan 28, 2024</span>
                <span>Expires: Jan 28, 2027</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Download className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                View
              </Button>
            </div>
          </div>

          {/* Contract Item 4 */}
          <div className="flex items-center gap-4 p-4 border rounded-xl hover:bg-muted/20 transition-colors">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-purple-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-1">
                <h4 className="font-semibold">Vendor Agreement - CloudServices Inc.</h4>
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">Active</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">Recurring</span>
              </div>
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <span>Vendor: CloudServices Inc.</span>
                <span>Monthly: $2,500</span>
                <span>Auto-renew: Enabled</span>
                <span>Next review: Mar 1, 2024</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Download className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                View
              </Button>
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-8">
          <p className="text-sm text-muted-foreground">
            Showing 1-10 of 248 contracts
          </p>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">Previous</Button>
            <Button variant="outline" size="sm">1</Button>
            <Button size="sm">2</Button>
            <Button variant="outline" size="sm">3</Button>
            <Button variant="outline" size="sm">Next</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Analytics Mockup Component
const AnalyticsMockup = () => {
  return (
    <div className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto">
      {/* Browser header */}
      <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
          <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-background rounded-lg px-4 py-2 text-sm text-muted-foreground border shadow-sm">
            <span className="text-green-600">🔒</span> https://app.averum.com/analytics
          </div>
        </div>
      </div>

      {/* App Navigation */}
      <div className="flex items-center gap-6 px-6 py-4 border-b bg-gradient-to-r from-muted/10 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="font-bold text-lg">Averum</span>
        </div>
        <nav className="flex items-center gap-8 text-sm">
          <span className="text-muted-foreground">Create</span>
          <span className="text-muted-foreground">Analysis</span>
          <span className="text-muted-foreground">Review</span>
          <span className="text-muted-foreground">Repository</span>
          <span className="text-primary font-semibold border-b-2 border-primary pb-2 px-1">
            Analytics
          </span>
        </nav>
      </div>

      <div className="p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h3 className="text-2xl font-bold mb-1">Contract Analytics Dashboard</h3>
            <p className="text-muted-foreground">Performance insights for January 2024</p>
          </div>
          <div className="flex items-center gap-3">
            <Select defaultValue="30-days">
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30-days">Last 30 days</SelectItem>
                <SelectItem value="90-days">Last 90 days</SelectItem>
                <SelectItem value="1-year">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-25 border border-blue-200 rounded-xl">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">248</p>
                <p className="text-sm text-muted-foreground">Total Contracts</p>
              </div>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-green-600">+12%</span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
          </div>

          <div className="p-6 bg-gradient-to-br from-green-50 to-green-25 border border-green-200 rounded-xl">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">94%</p>
                <p className="text-sm text-muted-foreground">Compliance Rate</p>
              </div>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-green-600">+3%</span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
          </div>

          <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-25 border border-purple-200 rounded-xl">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Clock className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">2.3</p>
                <p className="text-sm text-muted-foreground">Avg Review Time (days)</p>
              </div>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-green-600">-18%</span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
          </div>

          <div className="p-6 bg-gradient-to-br from-orange-50 to-orange-25 border border-orange-200 rounded-xl">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">$2.4M</p>
                <p className="text-sm text-muted-foreground">Contract Value</p>
              </div>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-green-600">+24%</span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Contract Types Chart */}
          <div className="p-6 border rounded-xl">
            <div className="flex items-center gap-3 mb-6">
              <PieChart className="w-5 h-5 text-primary" />
              <h4 className="text-lg font-semibold">Contract Types</h4>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span className="text-sm">Service Agreements</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">89</span>
                  <span className="text-sm text-muted-foreground ml-2">36%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span className="text-sm">Employment Contracts</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">67</span>
                  <span className="text-sm text-muted-foreground ml-2">27%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-purple-500 rounded"></div>
                  <span className="text-sm">NDAs</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">52</span>
                  <span className="text-sm text-muted-foreground ml-2">21%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-orange-500 rounded"></div>
                  <span className="text-sm">Vendor Agreements</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">40</span>
                  <span className="text-sm text-muted-foreground ml-2">16%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="p-6 border rounded-xl">
            <div className="flex items-center gap-3 mb-6">
              <Calendar className="w-5 h-5 text-primary" />
              <h4 className="text-lg font-semibold">Recent Activity</h4>
            </div>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Service Agreement signed</p>
                  <p className="text-xs text-muted-foreground">TechCorp Inc. • 2 hours ago</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">New contract created</p>
                  <p className="text-xs text-muted-foreground">Employment Contract • 4 hours ago</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Review requested</p>
                  <p className="text-xs text-muted-foreground">Vendor Agreement • 6 hours ago</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">AI analysis completed</p>
                  <p className="text-xs text-muted-foreground">NDA Template • 8 hours ago</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm font-medium">Contract approved</p>
                  <p className="text-xs text-muted-foreground">Service Agreement • 1 day ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserJourneyStory;
