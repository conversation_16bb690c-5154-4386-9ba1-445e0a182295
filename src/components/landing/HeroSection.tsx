import { Button } from '@/components/ui/button';
import { <PERSON>R<PERSON>, <PERSON>rkles, Shield, Users, Zap, Upload, FileText, AlertTriangle } from 'lucide-react';
import { useState } from 'react';

interface HeroSectionProps {
  onGetStarted: () => void;
}

const HeroSection = ({ onGetStarted }: HeroSectionProps) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showResults, setShowResults] = useState(true);

  const handleFileUpload = () => {
    setIsAnalyzing(true);
    setShowResults(false);
    setTimeout(() => {
      setIsAnalyzing(false);
      setShowResults(true);
    }, 3000);
  };
  return (
    <section className="relative min-h-screen flex items-center">
      {/* Removed isolated background decorations to use unified background */}

      <div className="relative w-full px-4 sm:px-6 lg:px-12 py-12 sm:py-16 lg:py-0">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            {/* Left side - Content */}
            <div className="text-center lg:text-left space-y-6 sm:space-y-8">
              <div className="space-y-4 sm:space-y-6">
                <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-primary/10 rounded-full text-xs sm:text-sm font-medium text-primary">
                  <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
                  AI-Powered Contract Management
                </div>

                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight leading-[1.1] sm:leading-[1.1] md:leading-[1.1] lg:leading-[1.1] xl:leading-[1.1] mb-6 sm:mb-8 pt-2">
                  Transform Your
                  <span className="block text-primary">Legal Workflow</span>
                </h1>

                <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground leading-relaxed max-w-2xl mx-auto lg:mx-0 mb-2">
                  Create, analyze, and manage contracts 40% faster with AI-powered insights.
                  Trusted by legal teams at leading companies worldwide.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Button
                  size="lg"
                  onClick={onGetStarted}
                  className="text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 h-auto font-medium w-full sm:w-auto"
                >
                  Start free trial
                  <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5 ml-2" />
                </Button>
              </div>

              {/* Trust indicators */}
              <div className="flex flex-col sm:flex-row flex-wrap items-center justify-center lg:justify-start gap-4 sm:gap-6 lg:gap-8 pt-4">
                <div className="flex items-center gap-2 text-xs sm:text-sm font-medium">
                  <Shield className="w-4 sm:w-5 h-4 sm:h-5 text-green-600" />
                  <span>SOC 2 Compliant</span>
                </div>
                <div className="flex items-center gap-2 text-xs sm:text-sm font-medium">
                  <Users className="w-4 sm:w-5 h-4 sm:h-5 text-blue-600" />
                  <span>10,000+ Users</span>
                </div>
                <div className="flex items-center gap-2 text-xs sm:text-sm font-medium">
                  <Zap className="w-4 sm:w-5 h-4 sm:h-5 text-yellow-600" />
                  <span>99.9% Uptime</span>
                </div>
              </div>
            </div>

            {/* Right side - Enhanced Product Screenshot */}
            <div className="relative mt-8 lg:mt-0">
              <div className="relative bg-background rounded-xl sm:rounded-2xl shadow-2xl border overflow-hidden max-w-lg mx-auto lg:max-w-none">
                {/* Browser header */}
                <div className="flex items-center gap-2 px-3 sm:px-6 py-3 sm:py-4 border-b bg-muted/50">
                  <div className="w-2 sm:w-3 h-2 sm:h-3 bg-muted-foreground rounded-full"></div>
                  <div className="w-2 sm:w-3 h-2 sm:h-3 bg-muted-foreground rounded-full"></div>
                  <div className="w-2 sm:w-3 h-2 sm:h-3 bg-muted-foreground rounded-full"></div>
                  <span className="text-xs sm:text-sm text-muted-foreground ml-2 sm:ml-4 truncate">Averum Contracts - Contract Analysis</span>
                </div>

                {/* Main interface */}
                <div className="p-3 sm:p-6 space-y-4 sm:space-y-6">
                  {/* Upload Area */}
                  {!showResults && (
                    <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-6 text-center space-y-4">
                      <div className="flex justify-center">
                        <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                          {isAnalyzing ? (
                            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Upload className="w-6 h-6 text-primary" />
                          )}
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium">
                          {isAnalyzing ? 'Analyzing contract...' : 'Drop your contract here'}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {isAnalyzing ? 'AI is scanning for risks and compliance issues' : 'PDF, DOC, or TXT files supported'}
                        </p>
                      </div>
                      {!isAnalyzing && (
                        <Button size="sm" onClick={handleFileUpload} className="text-xs">
                          <FileText className="w-3 h-3 mr-1" />
                          Choose File
                        </Button>
                      )}
                    </div>
                  )}

                  {showResults && (
                  <div>
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <div>
                      <h3 className="text-base sm:text-xl font-semibold leading-[1.3]">Service Agreement Analysis</h3>
                      <p className="text-xs sm:text-sm text-muted-foreground">Real-time AI contract review</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-muted text-foreground px-2 sm:px-3 py-1 rounded-full font-medium">
                        ✓ Analysis Complete
                      </span>
                    </div>
                  </div>

                  {/* Contract sections */}
                  <div className="space-y-2 sm:space-y-3">
                    <div className="p-3 sm:p-4 bg-green-50 border-l-4 border-green-400 rounded-r-lg">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-2 sm:w-3 h-2 sm:h-3 bg-green-600 rounded-full"></div>
                          <span className="text-sm sm:text-base font-medium leading-[1.3]">Service Agreement Terms</span>
                        </div>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded w-fit">Low Risk</span>
                      </div>
                      <p className="text-xs sm:text-sm text-green-700">
                        Standard terms detected. All clauses comply with industry standards.
                      </p>
                    </div>

                    <div className="p-3 sm:p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded-r-lg">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="w-3 h-3 text-yellow-600" />
                          <span className="text-sm sm:text-base font-medium leading-[1.3]">Scope of Work</span>
                        </div>
                        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded w-fit">Medium Risk</span>
                      </div>
                      <p className="text-xs sm:text-sm text-yellow-700">
                        Consider adding specific deliverables and timelines for clarity.
                      </p>
                      <Button size="sm" variant="outline" className="mt-2 text-xs h-6">
                        Fix Suggestion
                      </Button>
                    </div>

                    <div className="p-3 sm:p-4 bg-green-50 border-l-4 border-green-400 rounded-r-lg">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-2 sm:w-3 h-2 sm:h-3 bg-green-600 rounded-full"></div>
                          <span className="text-sm sm:text-base font-medium leading-[1.3]">Payment Terms</span>
                        </div>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded w-fit">Low Risk</span>
                      </div>
                      <p className="text-xs sm:text-sm text-green-700">
                        Payment terms are clearly defined and legally compliant.
                      </p>
                    </div>
                  </div>
                  </div>
                  )}

                  {/* AI Score */}
                  {showResults && (
                  <div className="flex items-center justify-between p-3 sm:p-4 bg-primary/5 rounded-lg">
                    <div>
                      <p className="text-xs sm:text-sm font-medium leading-[1.3]">Overall Compliance Score</p>
                      <p className="text-xs text-muted-foreground">Based on AI analysis</p>
                    </div>
                    <div className="text-right">
                      <div className="text-xl sm:text-2xl font-bold text-primary leading-[1.2]">92%</div>
                      <div className="text-xs text-muted-foreground">Excellent</div>
                    </div>
                  </div>
                  )}
                </div>
              </div>

              {/* Floating elements - adjusted for mobile */}
              <div className="absolute -top-2 sm:-top-4 -right-2 sm:-right-4 bg-primary text-primary-foreground px-2 sm:px-4 py-1 sm:py-2 rounded-md sm:rounded-lg shadow-lg text-xs sm:text-sm font-medium">
                AI Powered ✨
              </div>
              <div className="absolute -bottom-2 sm:-bottom-4 -left-2 sm:-left-4 bg-foreground text-background px-2 sm:px-4 py-1 sm:py-2 rounded-md sm:rounded-lg shadow-lg text-xs sm:text-sm font-medium">
                92% Compliant ✓
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
