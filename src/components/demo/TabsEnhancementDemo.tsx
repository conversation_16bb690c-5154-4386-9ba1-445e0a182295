import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";

export const TabsEnhancementDemo = () => {
  return (
    <div className="p-6 space-y-8 max-w-4xl mx-auto">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Tab Enhancement Demo</h1>
        <p className="text-muted-foreground">
          Subtle border enhancement on the tab container for better visual definition
        </p>
      </div>

      <div className="grid gap-8">
        {/* Enhanced Tabs (Default) */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold mb-2">Enhanced Tabs (Default)</h2>
            <p className="text-sm text-muted-foreground mb-4">
              The grey container now has a subtle 1.5px border for better visual definition
            </p>
          </div>
          
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Overview Content</h3>
                <p className="text-sm text-muted-foreground">
                  Notice the subtle border around the tab container above - it provides better visual separation while keeping the individual tab buttons unchanged.
                </p>
              </div>
            </TabsContent>
            <TabsContent value="analytics" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Analytics Content</h3>
                <p className="text-sm text-muted-foreground">
                  The enhancement is minimal and maintains the clean aesthetic.
                </p>
              </div>
            </TabsContent>
            <TabsContent value="reports" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Reports Content</h3>
                <p className="text-sm text-muted-foreground">
                  Works consistently in both light and dark modes.
                </p>
              </div>
            </TabsContent>
            <TabsContent value="settings" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Settings Content</h3>
                <p className="text-sm text-muted-foreground">
                  Follows the same border enhancement approach as Card components.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Original Tabs for Comparison */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold mb-2">Original Tabs (For Comparison)</h2>
            <p className="text-sm text-muted-foreground mb-4">
              Original styling without the enhanced border
            </p>
          </div>
          
          <Tabs defaultValue="tab1" className="w-full">
            <TabsList variant="default" className="grid w-full grid-cols-3">
              <TabsTrigger value="tab1">Tab 1</TabsTrigger>
              <TabsTrigger value="tab2">Tab 2</TabsTrigger>
              <TabsTrigger value="tab3">Tab 3</TabsTrigger>
            </TabsList>
            <TabsContent value="tab1" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Original Styling</h3>
                <p className="text-sm text-muted-foreground">
                  This shows the original tab styling without the enhanced border around the container.
                </p>
              </div>
            </TabsContent>
            <TabsContent value="tab2" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">No Border Enhancement</h3>
                <p className="text-sm text-muted-foreground">
                  Compare the visual definition with the enhanced version above.
                </p>
              </div>
            </TabsContent>
            <TabsContent value="tab3" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Comparison</h3>
                <p className="text-sm text-muted-foreground">
                  The enhanced version provides subtle but noticeable better visual clarity.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Compact Example */}
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold mb-2">Compact Enhanced Tabs</h2>
            <p className="text-sm text-muted-foreground mb-4">
              The enhancement works well in compact layouts too
            </p>
          </div>
          
          <Tabs defaultValue="active" className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="active">Active Items</TabsTrigger>
              <TabsTrigger value="archived">Archived</TabsTrigger>
            </TabsList>
            <TabsContent value="active" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Active Items</h3>
                <p className="text-sm text-muted-foreground">
                  The subtle border enhancement maintains consistency across different sizes.
                </p>
              </div>
            </TabsContent>
            <TabsContent value="archived" className="mt-4">
              <div className="p-4 rounded-lg bg-muted/50">
                <h3 className="font-medium mb-2">Archived Items</h3>
                <p className="text-sm text-muted-foreground">
                  Professional appearance with improved visual definition.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
