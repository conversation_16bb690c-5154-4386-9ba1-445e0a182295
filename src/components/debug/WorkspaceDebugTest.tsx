import React, { useState, useEffect } from 'react';
import { useAuth } from '@clerk/clerk-react';
import { WorkspaceService } from '../../services/api-services';
import { centralizedWorkspaceService } from '../../services/workspace-service';
import { useWorkspaces } from '../../hooks/useWorkspaces';
import { useWorkspaceStore } from '../../lib/workspace-store';

interface TestResult {
  category: string;
  test: string;
  status: 'success' | 'error' | 'warning' | 'info';
  result?: any;
  error?: any;
  timing?: number;
  timestamp: string;
  details?: any;
}

interface TestCategory {
  name: string;
  tests: TestResult[];
  expanded: boolean;
}

export const WorkspaceDebugTest: React.FC = () => {
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [runningTest, setRunningTest] = useState<string | null>(null);

  // Auth hooks
  const { getToken, isLoaded, userId, isSignedIn } = useAuth();

  // Workspace hooks
  const { fetchWorkspaces } = useWorkspaces();
  const workspaceStore = useWorkspaceStore();

  // Initialize categories
  useEffect(() => {
    setCategories([
      { name: 'Authentication Tests', tests: [], expanded: true },
      { name: 'API Connectivity Tests', tests: [], expanded: true },
      { name: 'Workspace Service Tests', tests: [], expanded: true },
      { name: 'Frontend State Tests', tests: [], expanded: true },
      { name: 'Environment & Configuration Tests', tests: [], expanded: true },
      { name: 'Error Simulation Tests', tests: [], expanded: true },
    ]);
  }, []);

  const addResult = (category: string, test: string, status: TestResult['status'], result?: any, error?: any, timing?: number, details?: any) => {
    const newResult: TestResult = {
      category,
      test,
      status,
      result,
      error,
      timing,
      timestamp: new Date().toISOString(),
      details
    };

    setCategories(prev => prev.map(cat =>
      cat.name === category
        ? { ...cat, tests: [...cat.tests, newResult] }
        : cat
    ));
  };

  const clearResults = () => {
    setCategories(prev => prev.map(cat => ({ ...cat, tests: [] })));
  };

  const toggleCategory = (categoryName: string) => {
    setCategories(prev => prev.map(cat =>
      cat.name === categoryName
        ? { ...cat, expanded: !cat.expanded }
        : cat
    ));
  };

  // Authentication Tests
  const runAuthenticationTests = async () => {
    setRunningTest('Authentication Tests');
    const category = 'Authentication Tests';

    // Test 1: Clerk Authentication State
    const startTime = Date.now();
    addResult(category, 'Clerk Authentication State', 'info', {
      isLoaded,
      userId,
      isSignedIn,
      hasUserId: !!userId
    }, null, Date.now() - startTime);

    // Test 2: Token Generation
    try {
      const tokenStart = Date.now();
      const token = await getToken();
      addResult(category, 'Token Generation', token ? 'success' : 'warning',
        token ? 'Token generated successfully' : 'No token generated',
        null, Date.now() - tokenStart, { tokenLength: token?.length });

      // Test 3: Token Format Validation
      if (token) {
        const parts = token.split('.');
        const isJWT = parts.length === 3;
        addResult(category, 'Token Format Validation', isJWT ? 'success' : 'error',
          isJWT ? 'Valid JWT format' : 'Invalid JWT format',
          null, 0, { parts: parts.length, isJWT });

        // Test 4: Token Payload Inspection
        try {
          const payload = JSON.parse(atob(parts[1]));
          const now = Math.floor(Date.now() / 1000);
          const isExpired = payload.exp && payload.exp < now;
          addResult(category, 'Token Expiration Check', isExpired ? 'error' : 'success',
            isExpired ? 'Token is expired' : 'Token is valid',
            null, 0, {
              exp: payload.exp,
              iat: payload.iat,
              currentTime: now,
              timeToExpiry: payload.exp ? payload.exp - now : null
            });
        } catch (e) {
          addResult(category, 'Token Payload Inspection', 'error', null, e);
        }
      }
    } catch (error) {
      addResult(category, 'Token Generation', 'error', null, error);
    }

    // Test 5: Development Mode Check
    const devMode = import.meta.env.VITE_ENVIRONMENT;
    addResult(category, 'Development Mode Check', 'info', `Environment: ${devMode}`, null, 0, {
      environment: devMode,
      isDevelopment: devMode === 'development'
    });

    setRunningTest(null);
  };

  // API Connectivity Tests
  const runAPIConnectivityTests = async () => {
    setRunningTest('API Connectivity Tests');
    const category = 'API Connectivity Tests';
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

    // Test 1: Basic Network Connectivity
    try {
      const startTime = Date.now();
      const response = await fetch(`${baseUrl.replace('/api', '')}/api/health`);
      const timing = Date.now() - startTime;
      const data = await response.json();
      addResult(category, 'Basic Network Connectivity', response.ok ? 'success' : 'error',
        `Status: ${response.status}, Response time: ${timing}ms`, null, timing, data);
    } catch (error) {
      addResult(category, 'Basic Network Connectivity', 'error', null, error);
    }

    // Test 2: CORS Preflight Request
    try {
      const startTime = Date.now();
      const response = await fetch(`${baseUrl}/workspaces`, {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'authorization,content-type'
        }
      });
      const timing = Date.now() - startTime;
      addResult(category, 'CORS Preflight Request', response.ok ? 'success' : 'error',
        `Status: ${response.status}, Response time: ${timing}ms`, null, timing, {
          allowOrigin: response.headers.get('access-control-allow-origin'),
          allowMethods: response.headers.get('access-control-allow-methods'),
          allowHeaders: response.headers.get('access-control-allow-headers')
        });
    } catch (error) {
      addResult(category, 'CORS Preflight Request', 'error', null, error);
    }

    // Test 3: API Base URL Configuration
    addResult(category, 'API Base URL Configuration', 'info', baseUrl, null, 0, {
      configuredUrl: baseUrl,
      envVariable: import.meta.env.VITE_API_BASE_URL,
      currentOrigin: window.location.origin
    });

    // Test 4: Different HTTP Methods
    const token = await getToken();
    const methods = ['GET', 'POST'];

    for (const method of methods) {
      try {
        const startTime = Date.now();
        const options: RequestInit = {
          method,
          headers: {
            'Authorization': `Bearer ${token || 'any-token'}`,
            'Content-Type': 'application/json',
            'Origin': window.location.origin
          }
        };

        if (method === 'POST') {
          options.body = JSON.stringify({
            name: `Debug Test ${Date.now()}`,
            description: 'Test workspace for debugging'
          });
        }

        const response = await fetch(`${baseUrl}/workspaces${method === 'GET' ? '/' : '/'}`, options);
        const timing = Date.now() - startTime;

        addResult(category, `${method} Request Test`, response.ok ? 'success' : 'warning',
          `Status: ${response.status}, Response time: ${timing}ms`, null, timing, {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
          });
      } catch (error) {
        addResult(category, `${method} Request Test`, 'error', null, error);
      }
    }

    setRunningTest(null);
  };

  // Workspace Service Tests
  const runWorkspaceServiceTests = async () => {
    setRunningTest('Workspace Service Tests');
    const category = 'Workspace Service Tests';

    // Test 1: Direct WorkspaceService.getWorkspaces()
    try {
      const startTime = Date.now();
      const token = await getToken();
      const response = await WorkspaceService.getWorkspaces(token || undefined);
      const timing = Date.now() - startTime;
      addResult(category, 'Direct WorkspaceService Call', 'success',
        `Success: ${response.data?.length || 0} workspaces`, null, timing, {
          dataLength: response.data?.length,
          hasData: !!response.data,
          responseKeys: Object.keys(response)
        });
    } catch (error) {
      addResult(category, 'Direct WorkspaceService Call', 'error', null, error);
    }

    // Test 2: Centralized Workspace Service
    try {
      const startTime = Date.now();
      const workspaces = await centralizedWorkspaceService.fetchWorkspaces(getToken);
      const timing = Date.now() - startTime;
      addResult(category, 'Centralized Service Call', 'success',
        `Success: ${workspaces.length} workspaces`, null, timing, {
          workspacesLength: workspaces.length,
          hasWorkspaces: workspaces.length > 0
        });
    } catch (error) {
      addResult(category, 'Centralized Service Call', 'error', null, error);
    }

    // Test 3: Request Deduplication Logic
    try {
      const startTime = Date.now();
      const promises = [
        centralizedWorkspaceService.fetchWorkspaces(getToken),
        centralizedWorkspaceService.fetchWorkspaces(getToken),
        centralizedWorkspaceService.fetchWorkspaces(getToken)
      ];
      const results = await Promise.allSettled(promises);
      const timing = Date.now() - startTime;
      const successful = results.filter(r => r.status === 'fulfilled').length;
      addResult(category, 'Request Deduplication Test', successful > 0 ? 'success' : 'error',
        `${successful}/3 requests successful`, null, timing, {
          totalRequests: 3,
          successful,
          results: results.map(r => r.status)
        });
    } catch (error) {
      addResult(category, 'Request Deduplication Test', 'error', null, error);
    }

    // Test 4: Caching Mechanism
    try {
      // Try to access cache through a test call
      const testCache = await centralizedWorkspaceService.fetchWorkspaces(getToken, false);
      addResult(category, 'Caching Mechanism Test', 'success',
        `Cache test completed with ${testCache.length} workspaces`, null, 0, {
          workspacesFromCache: testCache.length
        });
    } catch (error) {
      addResult(category, 'Caching Mechanism Test', 'error', null, error);
    }

    setRunningTest(null);
  };

  // Frontend State Tests
  const runFrontendStateTests = async () => {
    setRunningTest('Frontend State Tests');
    const category = 'Frontend State Tests';

    // Test 1: Workspace Store State
    addResult(category, 'Workspace Store State', 'info',
      `${workspaceStore.workspaces.length} workspaces in store`, null, 0, {
        workspacesCount: workspaceStore.workspaces.length,
        isLoading: workspaceStore.isLoading,
        error: workspaceStore.error,
        storeKeys: Object.keys(workspaceStore)
      });

    // Test 2: useWorkspaces Hook Test
    try {
      const startTime = Date.now();
      const result = await fetchWorkspaces();
      const timing = Date.now() - startTime;
      addResult(category, 'useWorkspaces Hook Test', 'success',
        `Hook returned ${result.length} workspaces`, null, timing, {
          workspacesLength: result.length
        });
    } catch (error) {
      addResult(category, 'useWorkspaces Hook Test', 'error', null, error);
    }

    // Test 3: Memory Leak Check
    const beforeMemory = (performance as any).memory?.usedJSHeapSize || 0;
    for (let i = 0; i < 10; i++) {
      try {
        await centralizedWorkspaceService.fetchWorkspaces(getToken);
      } catch (e) {
        // Ignore errors for memory test
      }
    }
    const afterMemory = (performance as any).memory?.usedJSHeapSize || 0;
    const memoryIncrease = afterMemory - beforeMemory;
    addResult(category, 'Memory Leak Check', memoryIncrease > 1000000 ? 'warning' : 'success',
      `Memory increase: ${(memoryIncrease / 1024).toFixed(2)} KB`, null, 0, {
        beforeMemory,
        afterMemory,
        memoryIncrease
      });

    setRunningTest(null);
  };

  // Environment & Configuration Tests
  const runEnvironmentTests = async () => {
    setRunningTest('Environment & Configuration Tests');
    const category = 'Environment & Configuration Tests';

    // Test 1: Environment Variables
    const envVars = {
      VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
      VITE_CLERK_PUBLISHABLE_KEY: import.meta.env.VITE_CLERK_PUBLISHABLE_KEY,
      VITE_ENVIRONMENT: import.meta.env.VITE_ENVIRONMENT,
      VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
      VITE_USE_NEW_DOCUMENT_ENGINE: import.meta.env.VITE_USE_NEW_DOCUMENT_ENGINE
    };

    Object.entries(envVars).forEach(([key, value]) => {
      addResult(category, `Environment Variable: ${key}`, value ? 'success' : 'warning',
        value || 'Not set', null, 0, { key, value });
    });

    // Test 2: API Client Configuration
    addResult(category, 'API Client Configuration', 'info', 'Configuration loaded', null, 0, {
      baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
      currentOrigin: window.location.origin,
      userAgent: navigator.userAgent
    });

    setRunningTest(null);
  };

  // Error Simulation Tests
  const runErrorSimulationTests = async () => {
    setRunningTest('Error Simulation Tests');
    const category = 'Error Simulation Tests';

    // Test 1: Network Failure Simulation
    try {
      await fetch('http://invalid-url-that-does-not-exist.com/api/workspaces');
      addResult(category, 'Network Failure Simulation', 'warning', 'Unexpected success', null, 0);
    } catch (error) {
      addResult(category, 'Network Failure Simulation', 'success', 'Network error caught correctly', error, 0);
    }

    // Test 2: Authentication Failure Simulation
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/workspaces/`, {
        headers: {
          'Authorization': 'Bearer invalid-token-12345',
          'Content-Type': 'application/json'
        }
      });
      addResult(category, 'Authentication Failure Simulation',
        response.status === 401 ? 'success' : 'warning',
        `Status: ${response.status}`, null, 0, { status: response.status });
    } catch (error) {
      addResult(category, 'Authentication Failure Simulation', 'error', null, error);
    }

    // Test 3: Request Cancellation Simulation
    try {
      const controller = new AbortController();
      setTimeout(() => controller.abort(), 100);

      await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/workspaces/`, {
        signal: controller.signal,
        headers: {
          'Authorization': `Bearer ${await getToken() || 'any-token'}`,
          'Content-Type': 'application/json'
        }
      });
      addResult(category, 'Request Cancellation Simulation', 'warning', 'Request completed unexpectedly', null, 0);
    } catch (error: any) {
      const isCancellation = error.name === 'AbortError';
      addResult(category, 'Request Cancellation Simulation', isCancellation ? 'success' : 'error',
        isCancellation ? 'Request cancelled correctly' : 'Unexpected error', error, 0);
    }

    setRunningTest(null);
  };

  // Run All Tests
  const runAllTests = async () => {
    setLoading(true);
    clearResults();

    await runAuthenticationTests();
    await runAPIConnectivityTests();
    await runWorkspaceServiceTests();
    await runFrontendStateTests();
    await runEnvironmentTests();
    await runErrorSimulationTests();

    setLoading(false);
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      case 'info': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '⚪';
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h2 className="text-3xl font-bold mb-6">Comprehensive Workspace Debug Test</h2>

      <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <h3 className="font-semibold mb-2">System Status:</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>Auth Loaded: <span className={isLoaded ? 'text-green-600' : 'text-red-600'}>{isLoaded ? 'Yes' : 'No'}</span></div>
          <div>User ID: <span className="font-mono">{userId || 'None'}</span></div>
          <div>Signed In: <span className={isSignedIn ? 'text-green-600' : 'text-red-600'}>{isSignedIn ? 'Yes' : 'No'}</span></div>
          <div>Running: <span className="font-mono">{runningTest || 'None'}</span></div>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={runAllTests}
          disabled={loading}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 font-semibold"
        >
          🚀 Run All Tests
        </button>
        <button
          onClick={runAuthenticationTests}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          🔐 Auth Tests
        </button>
        <button
          onClick={runAPIConnectivityTests}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          🌐 API Tests
        </button>
        <button
          onClick={runWorkspaceServiceTests}
          disabled={loading}
          className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50"
        >
          ⚙️ Service Tests
        </button>
        <button
          onClick={runFrontendStateTests}
          disabled={loading}
          className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
        >
          🎛️ State Tests
        </button>
        <button
          onClick={runEnvironmentTests}
          disabled={loading}
          className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600 disabled:opacity-50"
        >
          🔧 Env Tests
        </button>
        <button
          onClick={runErrorSimulationTests}
          disabled={loading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          💥 Error Tests
        </button>
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          🗑️ Clear
        </button>
      </div>

      {loading && (
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded">
          <p className="text-blue-600 dark:text-blue-300">
            🔄 Running tests... {runningTest && `(${runningTest})`}
          </p>
        </div>
      )}

      <div className="space-y-4">
        {categories.map((category) => (
          <div key={category.name} className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              onClick={() => toggleCategory(category.name)}
              className="w-full px-4 py-3 text-left font-semibold bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg flex justify-between items-center"
            >
              <span>{category.name} ({category.tests.length} tests)</span>
              <span>{category.expanded ? '▼' : '▶'}</span>
            </button>

            {category.expanded && (
              <div className="p-4 space-y-3">
                {category.tests.length === 0 ? (
                  <p className="text-gray-500 italic">No tests run yet</p>
                ) : (
                  category.tests.map((test, index) => (
                    <div key={index} className="border border-gray-100 dark:border-gray-600 rounded p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium flex items-center gap-2">
                          <span>{getStatusIcon(test.status)}</span>
                          {test.test}
                        </h4>
                        <div className="text-xs text-gray-500 text-right">
                          <div>{new Date(test.timestamp).toLocaleTimeString()}</div>
                          {test.timing && <div>{test.timing}ms</div>}
                        </div>
                      </div>

                      {test.result && (
                        <p className={`mt-1 ${getStatusColor(test.status)}`}>{test.result}</p>
                      )}

                      {test.error && (
                        <div className="text-red-600 mt-1">
                          <p className="font-medium">Error: {test.error.message || String(test.error)}</p>
                          {test.error.stack && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-sm">Stack trace</summary>
                              <pre className="text-xs mt-1 bg-red-50 dark:bg-red-900 p-2 rounded overflow-x-auto">
                                {test.error.stack}
                              </pre>
                            </details>
                          )}
                        </div>
                      )}

                      {test.details && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm text-gray-600">Details</summary>
                          <pre className="text-xs mt-1 bg-gray-50 dark:bg-gray-800 p-2 rounded overflow-x-auto">
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
