import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronRight, 
  Users, 
  Globe, 
  FileText, 
  ClipboardList, 
  Building, 
  Paperclip, 
  CheckCircle2, 
  ArrowRight 
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import DraftManager from "./DraftManager";

interface Step {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface ContractWizardPreviewProps {
  onStartNew: () => void;
  onSelectDraft: (draftId: string) => void;
}

const ContractWizardPreview: React.FC<ContractWizardPreviewProps> = ({
  onStartNew,
  onSelectDraft,
}) => {
  const navigate = useNavigate();

  // Define wizard steps
  const steps: Step[] = [
    {
      id: "parties",
      title: "Parties & Details",
      description: "Define who is involved in this contract",
      icon: <Users className="h-4 w-4" />,
    },
    {
      id: "jurisdiction",
      title: "Jurisdiction",
      description: "Specify the governing law and jurisdiction",
      icon: <Globe className="h-4 w-4" />,
    },
    {
      id: "terms",
      title: "Key Terms",
      description: "Define the main obligations and terms",
      icon: <FileText className="h-4 w-4" />,
    },
    {
      id: "clauses",
      title: "Legal Clauses",
      description: "Select and customize legal clauses",
      icon: <ClipboardList className="h-4 w-4" />,
    },
    {
      id: "industry",
      title: "Industry Specific",
      description: "Add industry-specific provisions",
      icon: <Building className="h-4 w-4" />,
    },
    {
      id: "attachments",
      title: "Attachments",
      description: "Upload supporting documents",
      icon: <Paperclip className="h-4 w-4" />,
    },
    {
      id: "review",
      title: "Review & Approval",
      description: "Review and set approval workflow",
      icon: <CheckCircle2 className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Wizard Steps Preview */}
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Contract Creation Wizard</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            {steps.slice(0, 4).map((step, index) => (
              <StepItem key={step.id} step={step} number={index + 1} />
            ))}
          </div>
          <div className="space-y-3">
            {steps.slice(4).map((step, index) => (
              <StepItem key={step.id} step={step} number={index + 5} />
            ))}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        <Button 
          onClick={onStartNew}
          className="flex-1"
        >
          Start New Contract
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          onClick={() => navigate('/app/repository')}
          className="flex-1"
        >
          Browse Templates
        </Button>
      </div>

      {/* Recent Drafts */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Recent Drafts</h3>
          <Badge variant="outline" className="text-xs">
            Continue where you left off
          </Badge>
        </div>
        <DraftManager onSelectDraft={onSelectDraft} />
      </div>
    </div>
  );
};

// Step item component
const StepItem: React.FC<{ step: Step; number: number }> = ({ step, number }) => {
  return (
    <div className="flex items-center p-3 border rounded-md hover:bg-muted/50 transition-colors">
      <div className="h-7 w-7 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3 text-xs font-medium">
        {number}
      </div>
      <div className="flex-1">
        <div className="flex items-center">
          {step.icon}
          <span className="ml-2 font-medium">{step.title}</span>
        </div>
        <p className="text-xs text-muted-foreground mt-0.5">
          {step.description}
        </p>
      </div>
      <ChevronRight className="h-4 w-4 text-muted-foreground" />
    </div>
  );
};

export default ContractWizardPreview;
