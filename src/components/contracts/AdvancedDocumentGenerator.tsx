import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Download,
  FileText,
  FileType,
  File,
  FileImage,
  Loader2,
  Settings,
  Palette,
  Type,
  CheckCircle,
} from "lucide-react";
import { useApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";

interface AdvancedDocumentGeneratorProps {
  contractId: string;
  contractTitle?: string;
  onDocumentGenerated?: (fileInfo: any) => void;
}

interface BrandingOptions {
  company_name: string;
  letterhead: boolean;
  footer_text: string;
  color_scheme: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
}

const AdvancedDocumentGenerator = ({
  contractId,
  contractTitle = "Contract",
  onDocumentGenerated,
}: AdvancedDocumentGeneratorProps) => {
  const { fetch } = useApi();
  const { toast } = useToast();

  // State for generation options
  const [format, setFormat] = useState("pdf");
  const [templateName, setTemplateName] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedFiles, setGeneratedFiles] = useState<any[]>([]);

  // State for branding options
  const [showBrandingOptions, setShowBrandingOptions] = useState(false);
  const [branding, setBranding] = useState<BrandingOptions>({
    company_name: "Averum Contracts",
    letterhead: true,
    footer_text: "Generated by Averum Advanced Document Generator",
    color_scheme: {
      primary: "#1e293b",
      secondary: "#64748b",
      accent: "#3b82f6",
    },
    fonts: {
      heading: "Arial, sans-serif",
      body: "Times New Roman, serif",
    },
  });

  // Format options with icons and descriptions
  const formatOptions = [
    {
      value: "pdf",
      label: "PDF Document",
      icon: FileText,
      description: "Professional PDF with advanced formatting",
      color: "text-red-500",
    },
    {
      value: "docx",
      label: "Word Document",
      icon: FileType,
      description: "Editable Microsoft Word document",
      color: "text-blue-500",
    },
    {
      value: "html",
      label: "HTML Document",
      icon: File,
      description: "Web-ready HTML with embedded CSS",
      color: "text-orange-500",
    },
    {
      value: "txt",
      label: "Plain Text",
      icon: FileText,
      description: "Simple text format for basic use",
      color: "text-gray-500",
    },
    {
      value: "markdown",
      label: "Markdown",
      icon: FileImage,
      description: "Markdown format for documentation",
      color: "text-purple-500",
    },
  ];

  const handleGenerate = async () => {
    setIsGenerating(true);

    try {
      const result = await fetch(
        async (token?: string) => {
          const params = new URLSearchParams({
            format_type: format,
            ...(templateName && { template_name: templateName }),
          });

          const response = await window.fetch(`/api/contracts/${contractId}/generate-document?${params}`, {
            method: "POST",
            headers: {
              'Authorization': token ? `Bearer ${token}` : '',
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          return {
            data,
            status: response.status,
            message: 'Document generated successfully'
          };
        },
        "Generating document...",
        "Failed to generate document"
      );

      if (result && result.data) {
        const newFile = {
          ...result.data.file_info,
          format: format.toUpperCase(),
          generated_at: new Date().toISOString(),
        };

        setGeneratedFiles(prev => [newFile, ...prev]);

        toast({
          title: "Document Generated",
          description: `${format.toUpperCase()} document generated successfully`,
        });

        if (onDocumentGenerated) {
          onDocumentGenerated(newFile);
        }
      }
    } catch (error) {
      console.error("Error generating document:", error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async (filePath: string, filename: string) => {
    try {
      const result = await fetch(
        async (token?: string) => {
          const response = await window.fetch(`/api/contracts/${contractId}/download-document/${filePath}`, {
            headers: {
              'Authorization': token ? `Bearer ${token}` : '',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          return {
            data,
            status: response.status,
            message: 'Download link retrieved'
          };
        },
        "Getting download link...",
        "Failed to get download link"
      );

      if (result?.data?.download_url) {
        // Open download URL in new tab
        window.open(result.data.download_url, '_blank');

        toast({
          title: "Download Started",
          description: `Downloading ${filename}`,
        });
      }
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Download Failed",
        description: "Failed to download file. Please try again.",
        variant: "destructive",
      });
    }
  };

  const updateBrandingColor = (colorType: keyof BrandingOptions['color_scheme'], value: string) => {
    setBranding(prev => ({
      ...prev,
      color_scheme: {
        ...prev.color_scheme,
        [colorType]: value,
      },
    }));
  };

  const updateBrandingFont = (fontType: keyof BrandingOptions['fonts'], value: string) => {
    setBranding(prev => ({
      ...prev,
      fonts: {
        ...prev.fonts,
        [fontType]: value,
      },
    }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Advanced Document Generator
          </CardTitle>
          <CardDescription>
            Generate professional documents from "{contractTitle}" in multiple formats with custom branding
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Document Format</Label>
            <RadioGroup value={format} onValueChange={setFormat} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {formatOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <Label
                      htmlFor={option.value}
                      className="flex items-center cursor-pointer flex-1 p-3 border rounded-lg hover:bg-muted/50"
                    >
                      <IconComponent className={`h-5 w-5 mr-3 ${option.color}`} />
                      <div className="flex-1">
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">{option.description}</div>
                      </div>
                    </Label>
                  </div>
                );
              })}
            </RadioGroup>
          </div>

          <Separator />

          {/* Template Selection */}
          <div className="space-y-3">
            <Label htmlFor="template">Custom Template (Optional)</Label>
            <Input
              id="template"
              placeholder="Enter custom template name"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              Leave empty to use the default professional template
            </p>
          </div>

          <Separator />

          {/* Branding Options Toggle */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Branding & Styling</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowBrandingOptions(!showBrandingOptions)}
              >
                <Settings className="h-4 w-4 mr-2" />
                {showBrandingOptions ? "Hide" : "Show"} Options
              </Button>
            </div>

            {showBrandingOptions && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                {/* Company Name */}
                <div className="space-y-2">
                  <Label htmlFor="company-name">Company Name</Label>
                  <Input
                    id="company-name"
                    value={branding.company_name}
                    onChange={(e) => setBranding(prev => ({ ...prev, company_name: e.target.value }))}
                  />
                </div>

                {/* Footer Text */}
                <div className="space-y-2">
                  <Label htmlFor="footer-text">Footer Text</Label>
                  <Textarea
                    id="footer-text"
                    value={branding.footer_text}
                    onChange={(e) => setBranding(prev => ({ ...prev, footer_text: e.target.value }))}
                    rows={2}
                  />
                </div>

                {/* Letterhead Option */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="letterhead"
                    checked={branding.letterhead}
                    onCheckedChange={(checked) => setBranding(prev => ({ ...prev, letterhead: !!checked }))}
                  />
                  <Label htmlFor="letterhead">Include letterhead</Label>
                </div>

                {/* Color Scheme */}
                <div className="space-y-3">
                  <Label className="flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    Color Scheme
                  </Label>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="space-y-1">
                      <Label htmlFor="primary-color" className="text-sm">Primary</Label>
                      <Input
                        id="primary-color"
                        type="color"
                        value={branding.color_scheme.primary}
                        onChange={(e) => updateBrandingColor('primary', e.target.value)}
                        className="h-10"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="secondary-color" className="text-sm">Secondary</Label>
                      <Input
                        id="secondary-color"
                        type="color"
                        value={branding.color_scheme.secondary}
                        onChange={(e) => updateBrandingColor('secondary', e.target.value)}
                        className="h-10"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="accent-color" className="text-sm">Accent</Label>
                      <Input
                        id="accent-color"
                        type="color"
                        value={branding.color_scheme.accent}
                        onChange={(e) => updateBrandingColor('accent', e.target.value)}
                        className="h-10"
                      />
                    </div>
                  </div>
                </div>

                {/* Font Selection */}
                <div className="space-y-3">
                  <Label className="flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    Typography
                  </Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <Label htmlFor="heading-font" className="text-sm">Heading Font</Label>
                      <Select value={branding.fonts.heading} onValueChange={(value) => updateBrandingFont('heading', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                          <SelectItem value="Helvetica, sans-serif">Helvetica</SelectItem>
                          <SelectItem value="Georgia, serif">Georgia</SelectItem>
                          <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="body-font" className="text-sm">Body Font</Label>
                      <Select value={branding.fonts.body} onValueChange={(value) => updateBrandingFont('body', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
                          <SelectItem value="Georgia, serif">Georgia</SelectItem>
                          <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                          <SelectItem value="Helvetica, sans-serif">Helvetica</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="w-full"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating {format.toUpperCase()}...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Generate {format.toUpperCase()} Document
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* Generated Files */}
      {generatedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Generated Documents
            </CardTitle>
            <CardDescription>
              Your recently generated documents are ready for download
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {generatedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary">{file.format}</Badge>
                    <div>
                      <p className="font-medium">{file.filename}</p>
                      <p className="text-sm text-muted-foreground">
                        Generated {new Date(file.generated_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(file.path, file.filename)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdvancedDocumentGenerator;
