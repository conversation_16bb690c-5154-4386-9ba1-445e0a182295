import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Button } from "../ui/button";
import { ArrowLeft } from "lucide-react";
import EnhancedAIAnalysis from "./EnhancedAIAnalysis";
import { RealTimeAnalysisProgress } from "../ai/RealTimeAnalysisProgress";
import { InteractiveAIInsights } from "../ai/InteractiveAIInsights";
import { MobileAIInterface } from "../ai/MobileAIInterface";

interface ContractAIAnalysisDashboardProps {
  contractId?: string;
  contractName?: string;
  isFetching?: boolean;
}

const ContractAIAnalysisDashboard: React.FC<ContractAIAnalysisDashboardProps> = ({
  contractId: propContractId,
  contractName,
  isFetching = false,
}) => {
  const { contractId: urlContractId } = useParams<{ contractId: string }>();
  const navigate = useNavigate();
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Use prop contractId or URL parameter
  const contractId = propContractId || urlContractId;

  const handleGoBack = () => {
    if (contractId) {
      navigate(`/app/contracts/${contractId}`);
    } else {
      navigate('/app/contracts');
    }
  };

  if (!contractId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground">No contract ID provided</p>
          <Button onClick={handleGoBack} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const handleAnalysisComplete = (results: any) => {
    setAnalysisResults(results);
    setIsAnalyzing(false);
  };

  const handleAnalysisError = (error: string) => {
    console.error('Analysis error:', error);
    setIsAnalyzing(false);
  };

  const startAnalysis = () => {
    setIsAnalyzing(true);
    setAnalysisResults(null);
  };

  // Mock insights data for InteractiveAIInsights
  const mockInsights = analysisResults ? [
    {
      id: '1',
      type: 'risk' as const,
      title: 'Unlimited Liability Clause',
      description: 'Contract contains unlimited liability which poses significant financial risk',
      confidence: 0.92,
      severity: 'high' as const,
      category: 'Legal Risk',
      explanation: 'This clause removes caps on financial liability, potentially exposing your organization to unlimited damages in case of breach or negligence.',
      evidence: [
        'Section 8.1: "Party shall be liable for all damages without limitation"',
        'No liability cap specified in indemnification clause'
      ],
      suggestions: [
        'Add liability cap of $100,000 or 12 months of contract value',
        'Include mutual liability limitations',
        'Add carve-outs for gross negligence and willful misconduct'
      ],
      impact: 'High financial exposure in case of contract disputes or performance issues'
    },
    {
      id: '2',
      type: 'opportunity' as const,
      title: 'Volume Discount Potential',
      description: 'Contract structure allows for volume-based pricing negotiations',
      confidence: 0.78,
      severity: 'medium' as const,
      category: 'Cost Optimization',
      explanation: 'The pricing structure and terms suggest opportunities for volume discounts that could reduce overall costs.',
      evidence: [
        'Tiered pricing structure mentioned in Schedule A',
        'Annual volume commitments referenced'
      ],
      suggestions: [
        'Negotiate volume discounts for orders over $50,000',
        'Include annual commitment discounts',
        'Add most favored nation pricing clause'
      ],
      impact: 'Potential cost savings of 10-15% with volume commitments'
    }
  ] : [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={handleGoBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Contract
        </Button>
        {contractName && (
          <div>
            <h1 className="text-2xl font-bold">AI Analysis</h1>
            <p className="text-muted-foreground">{contractName}</p>
          </div>
        )}
      </div>

      {/* Real-time Analysis Progress */}
      <RealTimeAnalysisProgress
        contractId={contractId}
        onComplete={handleAnalysisComplete}
        onError={handleAnalysisError}
        autoStart={false}
        showDetails={true}
      />

      {/* Interactive AI Insights */}
      {analysisResults && (
        <InteractiveAIInsights
          insights={mockInsights}
          onFeedback={(insightId, feedback, comment) => {
            console.log('Feedback received:', { insightId, feedback, comment });
          }}
          onExplainMore={(insightId) => {
            console.log('Explain more requested for:', insightId);
          }}
          showConfidenceScores={true}
          allowFeedback={true}
        />
      )}

      {/* Mobile AI Interface */}
      <MobileAIInterface
        contractId={contractId}
        analysisData={analysisResults}
        onAnalyze={startAnalysis}
        onShare={() => console.log('Share analysis')}
        onExport={() => console.log('Export analysis')}
        isAnalyzing={isAnalyzing}
        className="lg:hidden"
      />

      {/* Enhanced AI Analysis Component (fallback) */}
      <div className="hidden lg:block">
        <EnhancedAIAnalysis
          contractId={contractId}
          contractTitle={contractName || "Contract"}
        />
      </div>
    </div>
  );
};

export default ContractAIAnalysisDashboard;
