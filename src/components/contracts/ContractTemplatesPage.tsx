import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import RepositoryPage from "../repository/RepositoryPage";
import { ArrowLeft, Wand2 } from "lucide-react";

const ContractTemplatesPage = () => {
  const handleSelectTemplate = (templateId: string) => {
    // Navigate to the contract wizard with the selected template
    window.location.href = `/app/contracts/wizard?template=${templateId}`;
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="space-y-1">
          <p className="body-text-secondary">
            Select a template to start your contract
          </p>
        </div>
        <div className="flex spacing-tight">
          <Button
            variant="default"
            size="sm"
            className="flex items-center spacing-micro"
            onClick={() => (window.location.href = "/app/contracts/wizard")}
          >
            <Wand2 className="h-4 w-4" />
            Start from Scratch
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center spacing-micro"
            onClick={() => (window.location.href = "/app/contracts")}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Contracts
          </Button>
        </div>
      </div>

      <RepositoryPage onSelectTemplate={handleSelectTemplate} />
    </div>
  );
};

export default ContractTemplatesPage;
