import React, { useState, useEffect } from "react";
import { useAuth } from '@clerk/clerk-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
// Note: toast is provided via useToast hook if needed
// import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  ArrowRight,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Download,
  Eye,
  FileText,
  History,
  RotateCcw,
  User,
  GitCompare,
  <PERSON>,
  Plus,
  Minus,
  Edit,
} from "lucide-react";

// Types
interface ContractVersion {
  id: string;
  version_number: number;
  title: string;
  content: any;
  changes_summary?: string;
  change_details?: any;
  created_by: {
    id: string;
    name: string;
  };
  created_at: string;
  is_current: boolean;
  workspace_id: string;
}

interface VersionDiff {
  field: string;
  old_value: any;
  new_value: any;
  change_type: 'added' | 'removed' | 'modified';
}

interface VersionComparison {
  from_version: ContractVersion;
  to_version: ContractVersion;
  differences: VersionDiff[];
  summary: string;
}

interface ContractVersionComparisonProps {
  contractId: string;
  contractTitle: string;
  versions?: ContractVersion[];
  onViewVersion?: (versionId: string) => void;
  onRestoreVersion?: (versionId: string) => void;
  onDownloadVersion?: (versionId: string) => void;
}

const ContractVersionComparison: React.FC<ContractVersionComparisonProps> = ({
  contractId,
  contractTitle,
  versions = [],
  onViewVersion,
  onRestoreVersion,
  onDownloadVersion,
}) => {
  // State
  const [leftVersionId, setLeftVersionId] = useState<string>(versions.length > 1 ? versions[1].id : "");
  const [rightVersionId, setRightVersionId] = useState<string>(versions.length > 0 ? versions[0].id : "");
  const [diffView, setDiffView] = useState<"inline" | "side-by-side">("side-by-side");

  // Get versions by ID
  const getVersionById = (id: string) => versions.find(v => v.id === id);
  const leftVersion = getVersionById(leftVersionId);
  const rightVersion = getVersionById(rightVersionId);

  // Handle view version
  const handleViewVersion = (versionId: string) => {
    if (onViewVersion) {
      onViewVersion(versionId);
      const v = getVersionById(versionId);
      alert(`Viewing version ${v?.version_number ?? ''}`);
    }
  };

  // Handle restore version
  const handleRestoreVersion = (versionId: string) => {
    if (onRestoreVersion) {
      onRestoreVersion(versionId);
      const v = getVersionById(versionId);
      alert(`Restored to version ${v?.version_number ?? ''}`);
    }
  };

  // Handle download version
  const handleDownloadVersion = (versionId: string) => {
    if (onDownloadVersion) {
      onDownloadVersion(versionId);
      const v = getVersionById(versionId);
      alert(`Downloaded version ${v?.version_number ?? ''}`);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Mock function to generate diff between two versions
  const generateDiff = (oldContent: string, newContent: string) => {
    // In a real implementation, this would use a diff algorithm
    // For this mock, we'll just highlight some random parts

    // Split content into lines
    const oldLines = oldContent.split("\n");
    const newLines = newContent.split("\n");

    // Create diff for side-by-side view
    const sideBySideDiff = [];
    const maxLines = Math.max(oldLines.length, newLines.length);

    for (let i = 0; i < maxLines; i++) {
      const oldLine = i < oldLines.length ? oldLines[i] : "";
      const newLine = i < newLines.length ? newLines[i] : "";

      // Randomly mark some lines as changed (for demo purposes)
      const isChanged = Math.random() > 0.7;

      sideBySideDiff.push({
        lineNumber: i + 1,
        oldLine,
        newLine,
        status: isChanged ? "changed" : "unchanged",
      });
    }

    return {
      sideBySideDiff,
    };
  };

  // Generate diff if both versions are selected
  const diff = leftVersion && rightVersion
    ? generateDiff(leftVersion.content, rightVersion.content)
    : null;

  // Render side-by-side diff
  const renderSideBySideDiff = () => {
    if (!diff) return null;

    return (
      <div className="flex border rounded-md overflow-hidden">
        <div className="w-1/2 border-r">
          <div className="bg-muted p-2 text-sm font-medium border-b flex items-center justify-between">
            <div className="flex items-center">
              <History className="h-4 w-4 mr-2" />
              Version {leftVersion?.version_number}
            </div>
            <div className="text-xs text-muted-foreground">
              {leftVersion ? formatDate(leftVersion.created_at) : ''}
            </div>
          </div>
          <ScrollArea className="h-[600px]">
            <div className="p-4 font-mono text-sm">
              {diff.sideBySideDiff.map((line) => (
                <div
                  key={`left-${line.lineNumber}`}
                  className={`py-1 pl-2 -mr-2 ${line.status === "changed" ? "bg-red-100 dark:bg-red-900/20" : ""}`}
                >
                  <span className="text-muted-foreground mr-4">{line.lineNumber}</span>
                  {line.oldLine}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
        <div className="w-1/2">
          <div className="bg-muted p-2 text-sm font-medium border-b flex items-center justify-between">
            <div className="flex items-center">
              <History className="h-4 w-4 mr-2" />
              Version {rightVersion?.version_number}
            </div>
            <div className="text-xs text-muted-foreground">
              {rightVersion ? formatDate(rightVersion.created_at) : ''}
            </div>
          </div>
          <ScrollArea className="h-[600px]">
            <div className="p-4 font-mono text-sm">
              {diff.sideBySideDiff.map((line) => (
                <div
                  key={`right-${line.lineNumber}`}
                  className={`py-1 pl-2 -mr-2 ${line.status === "changed" ? "bg-green-100 dark:bg-green-900/20" : ""}`}
                >
                  <span className="text-muted-foreground mr-4">{line.lineNumber}</span>
                  {line.newLine}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
    );
  };

  // Render inline diff
  const renderInlineDiff = () => {
    if (!diff) return null;

    return (
      <div className="border rounded-md overflow-hidden">
        <div className="bg-muted p-2 text-sm font-medium border-b flex items-center justify-between">
          <div className="flex items-center">
            <History className="h-4 w-4 mr-2" />
            Changes from Version {leftVersion?.version_number} to Version {rightVersion?.version_number}
          </div>
        </div>
        <ScrollArea className="h-[600px]">
          <div className="p-4 font-mono text-sm">
            {diff.sideBySideDiff.map((line) => (
              <React.Fragment key={`inline-${line.lineNumber}`}>
                {line.status === "changed" && (
                  <>
                    <div className="py-1 pl-2 bg-red-100 dark:bg-red-900/20">
                      <span className="text-muted-foreground mr-4">- </span>
                      {line.oldLine}
                    </div>
                    <div className="py-1 pl-2 bg-green-100 dark:bg-green-900/20">
                      <span className="text-muted-foreground mr-4">+ </span>
                      {line.newLine}
                    </div>
                  </>
                )}
                {line.status === "unchanged" && (
                  <div className="py-1 pl-2">
                    <span className="text-muted-foreground mr-4">  </span>
                    {line.newLine}
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        </ScrollArea>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Version Comparison</CardTitle>
              <CardDescription>
                Compare different versions of {contractTitle}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setDiffView(diffView === "inline" ? "side-by-side" : "inline")}
              >
                {diffView === "inline" ? (
                  <>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Side by Side
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Inline View
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Previous Version</label>
                <Select value={leftVersionId} onValueChange={setLeftVersionId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a version" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.filter(v => v.id !== rightVersionId).map((version) => (
                      <SelectItem key={version.id} value={version.id}>
                        <div className="flex items-center">
                          <span className="mr-2">Version {version.version_number}</span>
                          <span className="text-xs text-muted-foreground">
                            ({formatDate(version.created_at)})
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-center">
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                  <ArrowRight className="h-5 w-5" />
                </div>
              </div>
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Current Version</label>
                <Select value={rightVersionId} onValueChange={setRightVersionId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a version" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.filter(v => v.id !== leftVersionId).map((version) => (
                      <SelectItem key={version.id} value={version.id}>
                        <div className="flex items-center">
                          <span className="mr-2">Version {version.version_number}</span>
                          <span className="text-xs text-muted-foreground">
                            ({formatDate(version.created_at)})
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {leftVersion && rightVersion ? (
              <div className="space-y-4">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                        {leftVersion.created_by.name?.slice(0,2).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-medium">{leftVersion.created_by.name}</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(leftVersion.created_at)}
                        </div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">Changes:</h4>
                      <ul className="text-sm space-y-1">
                        {(leftVersion.change_details || (leftVersion.changes_summary ? [leftVersion.changes_summary] : [])).map((change: any, i: number) => (
                          <li key={i} className="flex items-start gap-2">
                            <span className="text-muted-foreground">•</span>
                            <span>{typeof change === 'string' ? change : JSON.stringify(change)}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                        {rightVersion.created_by.name?.slice(0,2).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-medium">{rightVersion.created_by.name}</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(rightVersion.created_at)}
                        </div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">Changes:</h4>
                      <ul className="text-sm space-y-1">
                        {(rightVersion.change_details || (rightVersion.changes_summary ? [rightVersion.changes_summary] : [])).map((change: any, i: number) => (
                          <li key={i} className="flex items-start gap-2">
                            <span className="text-muted-foreground">•</span>
                            <span>{typeof change === 'string' ? change : JSON.stringify(change)}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewVersion(leftVersion.id)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Version {leftVersion.version_number}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadVersion(leftVersion.id)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  {!leftVersion.is_current && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleRestoreVersion(leftVersion.id)}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Restore Version {leftVersion.version_number}
                    </Button>
                  )}
                </div>

                <Tabs defaultValue="diff" className="w-full">
                  <TabsList className="grid w-full max-w-md grid-cols-2">
                    <TabsTrigger value="diff">Differences</TabsTrigger>
                    <TabsTrigger value="full">Full Document</TabsTrigger>
                  </TabsList>

                  <TabsContent value="diff" className="mt-4">
                    {diffView === "side-by-side" ? renderSideBySideDiff() : renderInlineDiff()}
                  </TabsContent>

                  <TabsContent value="full" className="mt-4">
                    <div className="border rounded-md overflow-hidden">
                      <div className="bg-muted p-2 text-sm font-medium border-b">
                        Full Document Comparison
                      </div>
                      <div className="p-4">
                        <p className="text-center text-muted-foreground">
                          Full document comparison would be displayed here
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <History className="h-12 w-12 text-muted-foreground mb-3" />
                <h3 className="text-lg font-medium mb-1">Select versions to compare</h3>
                <p className="text-sm text-muted-foreground">
                  Choose two different versions to see the changes between them
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export type { ContractVersion };
export default ContractVersionComparison;
