import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  AlertTriangle,
  CheckCircle,
  Download,
  Info,
  RefreshCw,
  Shield,
  Zap,
  Check,
  Loader2,
} from "lucide-react";
import { useApi } from "@/lib/api";
import { AIAnalysisService } from "@/services/api-services";
import type { AIAnalysisResult } from "@/services/api-types";
import { useToast } from "@/components/ui/use-toast";

interface ContractAIAnalysisProps {
  contractId: string;
}

const ContractAIAnalysis = ({ contractId }: ContractAIAnalysisProps) => {
  const { fetch } = useApi();
  const { toast } = useToast();

  const [isApplyingSuggestions, setIsApplyingSuggestions] = useState(false);
  const [isRunningAnalysis, setIsRunningAnalysis] = useState(false);
  const [appliedSuggestions, setAppliedSuggestions] = useState<string[]>([]);
  const [analysis, setAnalysis] = useState<AIAnalysisResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch analysis data when component mounts
  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!contractId) return;

      setLoading(true);
      setError(null);

      try {
        const result = await fetch(
          () => AIAnalysisService.getAnalysis(contractId),
          "Loading analysis...",
          "Failed to load analysis"
        );

        if (result) {
          setAnalysis(result);
        }
      } catch (err) {
        console.error("Error fetching analysis:", err);
        setError("Failed to load analysis. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();
  }, [contractId, fetch]);

  // Handle apply individual suggestion
  const handleApplySuggestion = async (suggestionId: string) => {
    try {
      const result = await fetch(
        () => AIAnalysisService.applySuggestions(contractId, [suggestionId]),
        "Applying suggestion...",
        "Failed to apply suggestion"
      );

      if (result) {
        setAppliedSuggestions(prev => [...prev, suggestionId]);
        toast({
          title: "Suggestion applied",
          description: "The suggestion has been applied to the contract.",
        });
      }
    } catch (error) {
      console.error("Error applying suggestion:", error);
      toast({
        title: "Error",
        description: "Failed to apply suggestion. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle apply all AI suggestions
  const handleApplySuggestions = async () => {
    if (!analysis) return;

    setIsApplyingSuggestions(true);

    try {
      const suggestionIds = analysis.suggestions.map(s => s.id);
      const result = await fetch(
        () => AIAnalysisService.applySuggestions(contractId, suggestionIds),
        "Applying suggestions...",
        "Failed to apply suggestions"
      );

      if (result) {
        setAppliedSuggestions(suggestionIds);
        toast({
          title: "All suggestions applied",
          description: "All AI suggestions have been applied to the contract.",
        });
      }
    } catch (error) {
      console.error("Error applying suggestions:", error);
      toast({
        title: "Error",
        description: "Failed to apply suggestions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsApplyingSuggestions(false);
    }
  };

  // Handle run fresh analysis
  const handleRunAnalysis = async () => {
    setIsRunningAnalysis(true);

    try {
      const result = await fetch(
        () => AIAnalysisService.runAnalysis(contractId, 'contract_analysis'),
        "Running analysis...",
        "Failed to run analysis"
      );

      if (result) {
        setAnalysis(result);
        toast({
          title: "Analysis complete",
          description: "Fresh AI analysis has been completed successfully.",
        });
      }
    } catch (error) {
      console.error("Error running analysis:", error);
      toast({
        title: "Error",
        description: "Failed to run analysis. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRunningAnalysis(false);
    }
  };

  // Handle export analysis
  const handleExportAnalysis = async () => {
    try {
      const result = await fetch(
        () => AIAnalysisService.exportAnalysis(contractId),
        "Exporting analysis...",
        "Failed to export analysis"
      );

      if (result && result.url) {
        // Open the export URL in a new tab
        window.open(result.url, '_blank');

        toast({
          title: "Export successful",
          description: "Analysis has been exported successfully.",
        });
      }
    } catch (error) {
      console.error("Error exporting analysis:", error);
      toast({
        title: "Error",
        description: "Failed to export analysis. Please try again.",
        variant: "destructive",
      });
    }
  };

  // If loading, show loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
        <h3 className="text-lg font-medium">Loading analysis...</h3>
        <p className="text-muted-foreground mt-2">
          Please wait while we fetch the AI analysis
        </p>
      </div>
    );
  }

  // If error, show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading analysis</h3>
        <p className="text-muted-foreground mt-2">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => {
            setLoading(true);
            setError(null);
            // Refetch analysis
            setTimeout(() => {
              setLoading(false);
            }, 1000);
          }}
        >
          Try Again
        </Button>
      </div>
    );
  }

  // If no analysis data, show empty state
  if (!analysis) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Shield className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No analysis available</h3>
        <p className="text-muted-foreground mt-2">
          Run an analysis to get AI insights for this contract
        </p>
        <Button
          variant="default"
          className="mt-4"
          onClick={handleRunAnalysis}
          disabled={isRunningAnalysis}
        >
          {isRunningAnalysis ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Run Analysis
            </>
          )}
        </Button>
      </div>
    );
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "high":
        return <Badge variant="destructive" className="text-xs py-0 px-2 h-5">High Risk</Badge>;
      case "medium":
        return (
          <Badge
            variant="secondary"
            className="bg-amber-100 text-amber-800 hover:bg-amber-100 text-xs py-0 px-2 h-5"
          >
            Medium Risk
          </Badge>
        );
      case "low":
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200 text-xs py-0 px-2 h-5"
          >
            Low Risk
          </Badge>
        );
      default:
        return null;
    }
  };

  const getComplianceStatusBadge = (status: string) => {
    switch (status) {
      case "compliant":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200 text-xs py-0 px-2 h-5"
          >
            Compliant
          </Badge>
        );
      case "warning":
        return (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-700 border-amber-200 text-xs py-0 px-2 h-5"
          >
            Warning
          </Badge>
        );
      case "non-compliant":
        return <Badge variant="destructive" className="text-xs py-0 px-2 h-5">Non-Compliant</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      {/* Header with action buttons */}
      <div className="flex justify-between items-center">
        <h2 className="text-base font-medium">AI Analysis & Insights</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportAnalysis}
            className="h-8 text-sm"
          >
            <Download className="h-3.5 w-3.5 mr-1.5" />
            Export
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleRunAnalysis}
            disabled={isRunningAnalysis}
            className="h-8 text-sm"
          >
            <RefreshCw className={`h-3.5 w-3.5 mr-1.5 ${isRunningAnalysis ? "animate-spin" : ""}`} />
            {isRunningAnalysis ? "Analyzing..." : "Run Analysis"}
          </Button>
        </div>
      </div>

      {/* Score cards */}
      <div className="grid grid-cols-3 gap-3">
        <Card className="overflow-hidden">
          <CardHeader className="p-3 pb-1.5">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
              <Shield className="h-4 w-4 text-amber-500" />
            </div>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="flex items-end justify-between">
              <div className="text-xl font-bold">{analysis.risk_score}</div>
              <div className="text-sm text-muted-foreground">/ 100</div>
            </div>
            <Progress value={analysis.risk_score} className="h-1.5 mt-1.5" />
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardHeader className="p-3 pb-1.5">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Compliance</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </div>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="flex items-end justify-between">
              <div className="text-xl font-bold">{analysis.compliance_score}%</div>
              <div className="text-sm text-muted-foreground"></div>
            </div>
            <Progress value={analysis.compliance_score} className="h-1.5 mt-1.5" />
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardHeader className="p-3 pb-1.5">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Language Clarity</CardTitle>
              <Info className="h-4 w-4 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent className="p-3 pt-0">
            <div className="flex items-end justify-between">
              <div className="text-xl font-bold">{analysis.language_clarity}</div>
              <div className="text-sm text-muted-foreground">/ 100</div>
            </div>
            <Progress value={analysis.language_clarity} className="h-1.5 mt-1.5" />
          </CardContent>
        </Card>
      </div>

      {/* Main content - 2 columns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Left column */}
        <div className="space-y-3">
          {/* Key Risks */}
          <Card className="overflow-hidden">
            <CardHeader className="p-3 pb-1.5">
              <CardTitle className="text-sm font-medium">Key Risks Identified</CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-1.5">
              <div className="space-y-2">
                {analysis.key_risks.map((risk) => (
                  <div key={risk.id} className="p-2 border rounded-md">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-amber-500 mr-1.5 flex-shrink-0" />
                        <div>
                          <h3 className="text-sm font-medium">{risk.title}</h3>
                          <p className="text-xs text-muted-foreground">
                            {risk.location}
                          </p>
                        </div>
                      </div>
                      <div>{getSeverityBadge(risk.severity)}</div>
                    </div>
                    <div className="mt-1.5">
                      <p className="text-sm">
                        <span className="font-medium">Recommendation:</span> {risk.recommendation}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* AI Suggestions */}
          <Card className="overflow-hidden">
            <CardHeader className="p-3 pb-1.5">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">AI Suggestions</CardTitle>
                <Button
                  size="sm"
                  variant="default"
                  className="h-7 text-xs px-2"
                  onClick={handleApplySuggestions}
                  disabled={isApplyingSuggestions}
                >
                  {isApplyingSuggestions ? "Applying..." : "Apply All"}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-3 pt-1.5">
              <div className="space-y-2">
                {analysis.suggestions.map((suggestion) => (
                  <div key={suggestion.id} className="flex items-center justify-between p-2 border rounded-md">
                    <div>
                      <div className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-1.5 ${
                          suggestion.severity === 'high' ? 'bg-red-500' :
                          suggestion.severity === 'medium' ? 'bg-amber-500' : 'bg-blue-500'
                        }`} />
                        <h3 className="text-sm font-medium">{suggestion.title}</h3>
                      </div>
                      <div className="flex items-center mt-0.5">
                        <p className="text-sm text-muted-foreground ml-3.5">
                          {suggestion.description}
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-7 w-7 p-0"
                      onClick={() => handleApplySuggestion(suggestion.id)}
                      disabled={appliedSuggestions.includes(suggestion.id)}
                    >
                      {appliedSuggestions.includes(suggestion.id) ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <Zap className="h-4 w-4 text-purple-500" />
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column */}
        <div className="space-y-3">
          {/* Extracted Clauses */}
          <Card className="overflow-hidden">
            <CardHeader className="p-3 pb-1.5">
              <CardTitle className="text-sm font-medium">Extracted Key Clauses</CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-1.5">
              <div className="space-y-2">
                {analysis.extracted_clauses.map((clause) => (
                  <div key={clause.id} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">{clause.title}</h3>
                      <span className="text-xs text-muted-foreground">{clause.location}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {clause.content}
                    </p>
                    <Separator className="my-1.5" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Compliance Check */}
          <Card className="overflow-hidden">
            <CardHeader className="p-3 pb-1.5">
              <CardTitle className="text-sm font-medium">Compliance Check</CardTitle>
            </CardHeader>
            <CardContent className="p-3 pt-1.5">
              <div className="space-y-2">
                {analysis.compliance_issues.map((issue) => (
                  <div
                    key={issue.id}
                    className="flex items-start justify-between p-2 border rounded-md"
                  >
                    <div>
                      <h3 className="text-sm font-medium">{issue.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {issue.details}
                      </p>
                    </div>
                    <div>{getComplianceStatusBadge(issue.status)}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ContractAIAnalysis;
