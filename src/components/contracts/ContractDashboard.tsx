import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertTriangle,
  ArrowUpRight,
  Calendar,
  CheckCircle,
  Clock,
  Loader2,
  Plus,
} from "lucide-react";
import ContractSummary from "./ContractSummary";
import { useApi } from "@/lib/api";
import { ContractService } from "@/services/api-services";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import type { Contract as ApiContract } from "@/services/api-types";

interface ContractDashboardProps {
  onCreateContract: () => void;
  onViewContract: (contractId: string) => void;
}

const ContractDashboard = ({
  onCreateContract,
  onViewContract,
}: ContractDashboardProps) => {
  const { fetchArray } = useApi();
  const { currentWorkspace } = useClerkWorkspace();

  // State for dashboard data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState({
    totalContracts: 0,
    pendingApprovals: 0,
    expiringSoon: 0,
    complianceRate: 0,
    recentContracts: [] as {
      id: string;
      title: string;
      type: string;
      status: string;
      createdBy: {
        name: string;
        avatar?: string;
        initials: string;
      };
      createdDate: string;
      counterparty: string;
    }[],
    expiringContracts: [] as {
      id: string;
      title: string;
      type: string;
      expiryDate: string;
      daysRemaining: number;
      counterparty: string;
    }[],
    pendingApprovalContracts: [] as {
      id: string;
      title: string;
      type: string;
      submittedBy: {
        name: string;
        avatar?: string;
        initials: string;
      };
      submittedDate: string;
      approvers: {
        name: string;
        status: string;
      }[];
    }[]
  });

  // Fetch contracts from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Only fetch contracts for the current workspace if one is selected
        // Only fetch contracts if we have a workspace
        if (!currentWorkspace) {
          setLoading(false);
          return;
        }

        const params = {
          workspace_id: currentWorkspace.id,
        };

        // Fetch all contracts
        const contractsResult = await fetchArray(
          () => ContractService.getContracts(params),
          "Loading dashboard data...",
          "Failed to load dashboard data"
        );

        if (contractsResult && contractsResult.length > 0) {
          // Process contracts for dashboard data
          const now = new Date();
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(now.getDate() + 30);

          // Count total contracts
          const totalContracts = contractsResult.length;

          // Count pending approvals
          const pendingApprovals = contractsResult.filter(
            (contract: ApiContract) => contract.status === 'pending_approval'
          ).length;

          // Find expiring contracts
          const expiringContracts = contractsResult
            .filter((contract: ApiContract) => {
              if (!contract.expiry_date) return false;
              const expiryDate = new Date(contract.expiry_date);
              return expiryDate > now && expiryDate <= thirtyDaysFromNow;
            })
            .map((contract: ApiContract) => {
              const expiryDate = new Date(contract.expiry_date!);
              const daysRemaining = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

              return {
                id: contract.id,
                title: contract.title,
                type: contract.type,
                expiryDate: contract.expiry_date!,
                daysRemaining,
                counterparty: contract.counterparty || 'N/A'
              };
            })
            .sort((a, b) => a.daysRemaining - b.daysRemaining)
            .slice(0, 3);

          // Find recent contracts
          const recentContracts = [...contractsResult]
            .sort((a: ApiContract, b: ApiContract) => {
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            })
            .slice(0, 2)
            .map((contract: ApiContract) => ({
              id: contract.id,
              title: contract.title,
              type: contract.type,
              status: contract.status,
              createdBy: {
                name: contract.created_by?.name || 'Unknown',
                // Generate initials from name
                initials: contract.created_by?.name
                  ? contract.created_by.name
                      .split(' ')
                      .map(n => n[0])
                      .join('')
                      .toUpperCase()
                      .substring(0, 2)
                  : 'UN',
                // Generate avatar from name
                avatar: contract.created_by?.name
                  ? `https://api.dicebear.com/7.x/avataaars/svg?seed=${contract.created_by.name.toLowerCase().replace(/\s/g, '')}`
                  : undefined
              },
              createdDate: contract.created_at,
              counterparty: contract.counterparty || 'N/A'
            }));

          // Find pending approval contracts
          const pendingApprovalContracts = contractsResult
            .filter((contract: ApiContract) => contract.status === 'pending_approval' && contract.approvers && contract.approvers.length > 0)
            .slice(0, 2)
            .map((contract: ApiContract) => ({
              id: contract.id,
              title: contract.title,
              type: contract.type,
              submittedBy: {
                name: contract.created_by?.name || 'Unknown',
                // Generate initials from name
                initials: contract.created_by?.name
                  ? contract.created_by.name
                      .split(' ')
                      .map(n => n[0])
                      .join('')
                      .toUpperCase()
                      .substring(0, 2)
                  : 'UN',
                // Generate avatar from name
                avatar: contract.created_by?.name
                  ? `https://api.dicebear.com/7.x/avataaars/svg?seed=${contract.created_by.name.toLowerCase().replace(/\s/g, '')}`
                  : undefined
              },
              submittedDate: contract.created_at,
              approvers: contract.approvers!.map(approver => ({
                name: approver.name,
                status: approver.status
              }))
            }));

          // Calculate compliance rate (for demo purposes, we'll use a simple calculation)
          // In a real app, this would be based on more complex criteria
          const activeContracts = contractsResult.filter(
            (contract: ApiContract) => contract.status === 'active'
          ).length;
          const complianceRate = activeContracts > 0
            ? Math.round((activeContracts / totalContracts) * 100)
            : 0;

          // Update dashboard data
          setDashboardData({
            totalContracts,
            pendingApprovals,
            expiringSoon: expiringContracts.length,
            complianceRate,
            recentContracts,
            expiringContracts,
            pendingApprovalContracts
          });
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentWorkspace?.id]); // Only depend on workspace ID to prevent infinite loops

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Status badge helper function (used in the component)
  /* const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            Approved
          </Badge>
        );
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-700 border-amber-200"
          >
            Pending
          </Badge>
        );
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  }; */

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Contract Dashboard</h2>
        <Button onClick={onCreateContract}>
          <Plus className="mr-2 h-4 w-4" />
          New Contract
        </Button>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading dashboard data...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch your dashboard data
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading dashboard</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Contracts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData.totalContracts}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {currentWorkspace ? `In ${currentWorkspace.name}` : 'Across all workspaces'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Approvals
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData.pendingApprovals}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Awaiting review
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData.expiringSoon}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Within next 30 days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Compliance Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-end gap-1">
                  <div className="text-2xl font-bold">
                    {dashboardData.complianceRate}%
                  </div>
                </div>
                <Progress
                  value={dashboardData.complianceRate}
                  className="h-1 mt-2"
                />
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Recent Contracts */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Contracts</CardTitle>
                  <CardDescription>
                    Your recently created or modified contracts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {dashboardData.recentContracts.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                      No recent contracts found
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {dashboardData.recentContracts.map((contract) => (
                        <div
                          key={contract.id}
                          className="flex items-start justify-between card-item"
                          onClick={() => onViewContract(contract.id)}
                        >
                          <div className="flex items-start gap-3">
                            <Avatar className="h-8 w-8">
                              {contract.createdBy.avatar ? (
                                <AvatarImage
                                  src={contract.createdBy.avatar}
                                  alt={contract.createdBy.name}
                                />
                              ) : (
                                <AvatarFallback>
                                  {contract.createdBy.initials}
                                </AvatarFallback>
                              )}
                            </Avatar>
                            <div>
                              <div className="font-medium">{contract.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {contract.type} • {contract.counterparty} • Created{" "}
                                {formatDate(contract.createdDate)}
                              </div>
                            </div>
                          </div>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <ArrowUpRight className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-4 text-center">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => (window.location.href = "/contracts")}
                    >
                      View All Contracts
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Featured Contract</CardTitle>
                  <CardDescription>
                    Recently updated contract that needs your attention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {dashboardData.recentContracts.length > 0 ? (
                    <ContractSummary
                      contractId={dashboardData.recentContracts[0].id}
                      onViewDetails={() => onViewContract(dashboardData.recentContracts[0].id)}
                    />
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      No contracts available
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Expiring & Pending */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-amber-500 mr-2" />
                    <CardTitle>Expiring Soon</CardTitle>
                  </div>
                  <CardDescription>
                    Contracts expiring in the next 30 days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {dashboardData.expiringContracts.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                      No contracts expiring soon
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {dashboardData.expiringContracts.map((contract) => (
                        <div
                          key={contract.id}
                          className="card-item"
                          onClick={() => onViewContract(contract.id)}
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="font-medium">{contract.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {contract.type} • {contract.counterparty}
                              </div>
                            </div>
                            <Badge
                              variant={
                                contract.daysRemaining <= 14
                                  ? "destructive"
                                  : "outline"
                              }
                            >
                              {contract.daysRemaining} days
                            </Badge>
                          </div>
                          <div className="mt-2 text-sm">
                            <span className="text-muted-foreground">Expires:</span>{" "}
                            {formatDate(contract.expiryDate)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-blue-500 mr-2" />
                    <CardTitle>Pending Approvals</CardTitle>
                  </div>
                  <CardDescription>
                    Contracts awaiting approval
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {dashboardData.pendingApprovalContracts.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                      No pending approvals
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {dashboardData.pendingApprovalContracts.map((contract) => (
                        <div
                          key={contract.id}
                          className="card-item"
                          onClick={() => onViewContract(contract.id)}
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="font-medium">{contract.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {contract.type} • Submitted{" "}
                                {formatDate(contract.submittedDate)}
                              </div>
                            </div>
                            <Avatar className="h-6 w-6">
                              {contract.submittedBy.avatar ? (
                                <AvatarImage
                                  src={contract.submittedBy.avatar}
                                  alt={contract.submittedBy.name}
                                />
                              ) : (
                                <AvatarFallback>
                                  {contract.submittedBy.initials}
                                </AvatarFallback>
                              )}
                            </Avatar>
                          </div>
                          <div className="mt-2 flex items-center gap-2">
                            {contract.approvers.map((approver, index) => (
                              <div key={index} className="flex items-center">
                                {approver.status === "approved" ? (
                                  <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                                ) : (
                                  <AlertTriangle className="h-3 w-3 text-amber-500 mr-1" />
                                )}
                                <span className="text-xs">{approver.name}</span>
                                {index < contract.approvers.length - 1 && (
                                  <span className="mx-1 text-muted-foreground">
                                    →
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-4 text-center">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => (window.location.href = "/approvals")}
                    >
                      View All Approvals
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ContractDashboard;
