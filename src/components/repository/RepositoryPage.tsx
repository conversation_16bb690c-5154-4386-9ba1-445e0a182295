import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ModernDocumentRepository from "./ModernDocumentRepository";
import AdvancedDocumentSearch from "../documents/AdvancedDocumentSearch";
import DocumentAnalyticsDashboard from "../documents/DocumentAnalyticsDashboard";
import { ArrowLeft, Upload, FolderPlus, MoreHorizontal, Plus, Search, BarChart3, FolderOpen } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";

interface RepositoryPageProps {
  onSelectTemplate?: (templateId: string) => void;
}

const RepositoryPage = ({ onSelectTemplate }: RepositoryPageProps) => {
  const { currentWorkspace } = useClerkWorkspace();
  const [activeTab, setActiveTab] = useState("documents");

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="space-y-1">
          <h1 className="page-title">Document Repository</h1>
          <p className="page-description">
            Manage, search, and analyze your documents with AI-powered tools
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="default"
            size="sm"
            className="flex items-center gap-1.5"
            onClick={() => {
              // TODO: Implement new template functionality
              console.log("Create new template");
            }}
          >
            <Plus className="h-4 w-4" />
            New Template
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1.5">
                <MoreHorizontal className="h-4 w-4" />
                More Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => console.log("Upload document")}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => console.log("Create new folder")}>
                <FolderPlus className="h-4 w-4 mr-2" />
                New Folder
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => (window.location.href = "/app")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Enhanced Repository with Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            AI Search
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-6">
          <ModernDocumentRepository onSelectTemplate={onSelectTemplate} />
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          {currentWorkspace ? (
            <AdvancedDocumentSearch
              workspaceId={currentWorkspace.id}
              onResultSelect={(result) => {
                console.log("Selected document:", result);
                // TODO: Handle document selection (open preview, etc.)
              }}
            />
          ) : (
            <div className="flex items-center justify-center p-8">
              <p className="text-muted-foreground">Please select a workspace to search documents</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {currentWorkspace ? (
            <DocumentAnalyticsDashboard workspaceId={currentWorkspace.id} />
          ) : (
            <div className="flex items-center justify-center p-8">
              <p className="text-muted-foreground">Please select a workspace to view analytics</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RepositoryPage;
