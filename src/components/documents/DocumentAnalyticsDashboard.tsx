import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { 
  FileText, 
  TrendingUp, 
  Download, 
  Upload, 
  Eye, 
  Search,
  Calendar,
  Filter,
  RefreshCw,
  Loader2
} from "lucide-react";
import { useAuth } from "@clerk/clerk-react";
import { toast } from "sonner";

interface DocumentAnalytics {
  total_documents: number;
  file_types: Record<string, number>;
  upload_trends: Record<string, number>;
  period_days: number;
}

interface DocumentAnalyticsDashboardProps {
  workspaceId: string;
}

const DocumentAnalyticsDashboard = ({ workspaceId }: DocumentAnalyticsDashboardProps) => {
  const { getToken } = useAuth();
  const [analytics, setAnalytics] = useState<DocumentAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<number>(30);
  const [refreshing, setRefreshing] = useState(false);

  // Color palette for charts
  const colors = [
    "#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#00ff00", 
    "#ff00ff", "#00ffff", "#ff0000", "#0000ff", "#ffff00"
  ];

  // Load analytics data
  const loadAnalytics = async (days: number = period) => {
    try {
      setLoading(true);
      const token = await getToken();

      const response = await fetch(
        `/api/documents/analytics/usage?workspace_id=${workspaceId}&days=${days}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to load analytics');
      }

      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Analytics error:', error);
      toast.error('Failed to load document analytics');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh analytics
  const refreshAnalytics = async () => {
    setRefreshing(true);
    await loadAnalytics();
  };

  // Prepare chart data
  const getFileTypeChartData = () => {
    if (!analytics?.file_types) return [];
    
    return Object.entries(analytics.file_types).map(([type, count]) => ({
      name: type.toUpperCase(),
      value: count,
      percentage: Math.round((count / analytics.total_documents) * 100)
    }));
  };

  const getUploadTrendData = () => {
    if (!analytics?.upload_trends) return [];
    
    return Object.entries(analytics.upload_trends)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, count]) => ({
        date: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        uploads: count
      }));
  };

  // Calculate summary stats
  const getSummaryStats = () => {
    if (!analytics) return null;

    const uploadTrendData = getUploadTrendData();
    const avgUploadsPerDay = uploadTrendData.length > 0 
      ? Math.round(analytics.total_documents / analytics.period_days * 10) / 10
      : 0;

    const mostCommonType = analytics.file_types 
      ? Object.entries(analytics.file_types).reduce((a, b) => a[1] > b[1] ? a : b)[0]
      : 'N/A';

    return {
      totalDocuments: analytics.total_documents,
      avgUploadsPerDay,
      mostCommonType: mostCommonType.toUpperCase(),
      fileTypeCount: Object.keys(analytics.file_types || {}).length
    };
  };

  useEffect(() => {
    if (workspaceId) {
      loadAnalytics();
    }
  }, [workspaceId, period]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading analytics...</span>
      </div>
    );
  }

  const summaryStats = getSummaryStats();
  const fileTypeData = getFileTypeChartData();
  const uploadTrendData = getUploadTrendData();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Analytics</h2>
          <p className="text-muted-foreground">
            Insights and trends for your document repository
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={period.toString()} onValueChange={(value) => setPeriod(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="icon"
            onClick={refreshAnalytics}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summaryStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.totalDocuments}</div>
              <p className="text-xs text-muted-foreground">
                In the last {period} days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Uploads/Day</CardTitle>
              <Upload className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.avgUploadsPerDay}</div>
              <p className="text-xs text-muted-foreground">
                Daily average
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Most Common Type</CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.mostCommonType}</div>
              <p className="text-xs text-muted-foreground">
                File type
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">File Types</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.fileTypeCount}</div>
              <p className="text-xs text-muted-foreground">
                Different types
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* File Types Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>File Types Distribution</CardTitle>
            <CardDescription>
              Breakdown of documents by file type
            </CardDescription>
          </CardHeader>
          <CardContent>
            {fileTypeData.length > 0 ? (
              <div className="space-y-4">
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={fileTypeData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {fileTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                
                <div className="space-y-2">
                  {fileTypeData.map((item, index) => (
                    <div key={item.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: colors[index % colors.length] }}
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{item.value}</span>
                        <Badge variant="secondary" className="text-xs">
                          {item.percentage}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                No data available
              </div>
            )}
          </CardContent>
        </Card>

        {/* Upload Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Trends</CardTitle>
            <CardDescription>
              Document uploads over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            {uploadTrendData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={uploadTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="uploads" 
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                No upload data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Insights & Recommendations</CardTitle>
          <CardDescription>
            AI-powered insights about your document repository
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {summaryStats && summaryStats.totalDocuments > 0 ? (
              <>
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-400">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">Repository Health</h4>
                  <p className="text-sm text-blue-700 dark:text-blue-200 mt-1">
                    Your document repository contains {summaryStats.totalDocuments} documents across {summaryStats.fileTypeCount} different file types.
                    {summaryStats.avgUploadsPerDay > 1 && " You have a healthy upload rate."}
                  </p>
                </div>
                
                {summaryStats.fileTypeCount > 5 && (
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border-l-4 border-yellow-400">
                    <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Organization Tip</h4>
                    <p className="text-sm text-yellow-700 dark:text-yellow-200 mt-1">
                      Consider organizing your documents into folders by type or project to improve searchability.
                    </p>
                  </div>
                )}
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-400">
                  <h4 className="font-medium text-green-900 dark:text-green-100">Search Optimization</h4>
                  <p className="text-sm text-green-700 dark:text-green-200 mt-1">
                    Use the AI-powered semantic search to find documents by meaning, not just keywords. 
                    Try searching for concepts like "payment terms" or "liability clauses".
                  </p>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No documents found for the selected period.</p>
                <p className="text-sm">Upload some documents to see analytics.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentAnalyticsDashboard;
