import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardDescription, CardFooter, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Plus,
  Search,
  MoreHorizontal,
  Users,
  Settings,
  Archive,
  Building2,
  Loader2,
  CheckCircle2,
} from "lucide-react";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Workspace } from "@/types/workspace";
import { useToast } from "@/components/ui/use-toast";
import WorkspaceForm from "./WorkspaceForm";
import UserAccessManager from "./UserAccessManager";

const WorkspacePage = () => {
  const {
    userWorkspaces,
    currentWorkspace,
    setCurrentWorkspace,
    deleteWorkspace,
    getWorkspaceById,
    getUserWorkspaces,
    canAccessWorkspace,
    isLoading,
  } = useClerkWorkspace();

  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isManageUsersOpen, setIsManageUsersOpen] = useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null);
  const [switchingWorkspace, setSwitchingWorkspace] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  // Get accessible workspaces (only workspaces the user has access to)
  const accessibleWorkspaces = getUserWorkspaces();

  // Filter workspaces based on search and access control
  const filteredWorkspaces = accessibleWorkspaces.filter((workspace) => {
    // Check if user has access to this workspace
    const hasAccess = canAccessWorkspace(workspace.id);
    if (!hasAccess) return false;

    // Apply search filter
    const matchesSearch = searchQuery === "" ||
      workspace.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      workspace.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  // Generate workspace initials
  const getWorkspaceInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate workspace avatar color
  const getWorkspaceColor = (name: string) => {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-orange-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-teal-500",
      "bg-red-500",
    ];
    const index = name.length % colors.length;
    return colors[index];
  };

  // Handle workspace switching with loading state
  const handleSwitchWorkspace = async (workspace: Workspace) => {
    if (workspace.id === currentWorkspace?.id) return;

    setSwitchingWorkspace(workspace.id);
    try {
      setCurrentWorkspace(workspace);
      toast({
        title: "Workspace switched",
        description: `Now working in ${workspace.name}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to switch workspace",
        variant: "destructive",
      });
    } finally {
      setSwitchingWorkspace(null);
    }
  };

  // Handle workspace deletion
  const handleDeleteWorkspace = async (workspace: Workspace) => {
    try {
      await deleteWorkspace(workspace.id);
      toast({
        title: "Workspace deleted",
        description: `${workspace.name} has been deleted`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete workspace",
        variant: "destructive",
      });
    }
  };

  // Handle workspace editing
  const handleEditWorkspace = (workspace: Workspace) => {
    setSelectedWorkspace(workspace);
    setIsEditDialogOpen(true);
  };

  // Handle manage users
  const handleManageUsers = (workspace: Workspace) => {
    setSelectedWorkspace(workspace);
    setIsManageUsersOpen(true);
  };

  // Format last activity (mock data for now)
  const getLastActivity = () => {
    // This would come from real data
    const activities = ["2 hours ago", "1 day ago", "3 days ago", "1 week ago"];
    return activities[Math.floor(Math.random() * activities.length)];
  };

  // Get user role in workspace (mock data for now)
  const getUserRole = () => {
    // This would come from real data
    const roles = ["Owner", "Admin", "Member"];
    return roles[Math.floor(Math.random() * roles.length)];
  };

  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          {showSettings && (
            <Button variant="outline" onClick={() => setShowSettings(false)}>
              Back to Workspaces
            </Button>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {!showSettings && (
            <>
              <Button variant="outline" onClick={() => setShowSettings(true)}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                New Workspace
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search Bar */}
      {accessibleWorkspaces.length > 0 && (
        <div className="relative mb-6 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workspaces..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      )}

      {/* Settings View */}
      {showSettings ? (
        <Card>
          <CardHeader>
            <CardTitle>Workspace Settings</CardTitle>
            <CardDescription>
              Configure global settings for all workspaces
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="defaultWorkspace">Default Workspace</Label>
              <Select
                defaultValue={currentWorkspace?.id || ""}
                onValueChange={(value) => {
                  const workspace = getWorkspaceById(value);
                  if (workspace) {
                    setCurrentWorkspace(workspace);
                    toast({
                      title: "Default workspace updated",
                      description: `${workspace.name} is now your default workspace`,
                    });
                  }
                }}
              >
                <SelectTrigger id="defaultWorkspace">
                  <SelectValue placeholder="Select default workspace" />
                </SelectTrigger>
                <SelectContent>
                  {accessibleWorkspaces.length > 0 ? (
                    accessibleWorkspaces.map((workspace) => (
                      <SelectItem key={workspace.id} value={workspace.id}>
                        {workspace.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>
                      No accessible workspaces
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                This workspace will be selected by default when you log in
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              {userWorkspaces.length} workspaces available
            </div>
          </CardFooter>
        </Card>
      ) : (
        <>
          {/* Loading State */}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-3 text-muted-foreground">Loading workspaces...</span>
            </div>
          ) : (
            <>
              {/* Empty State */}
              {accessibleWorkspaces.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="rounded-full bg-muted p-6 mb-6">
                    <Building2 className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No workspaces found</h3>
                  <p className="text-muted-foreground mb-6 max-w-md">
                    Create your first workspace to get started with contract management
                  </p>
                  <Button onClick={() => setIsCreateDialogOpen(true)} size="lg">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Workspace
                  </Button>
                </div>
              ) : (
                <>
                  {/* Workspace Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredWorkspaces.map((workspace) => (
                      <Card
                        key={workspace.id}
                        className={`group hover:shadow-lg transition-all duration-200 cursor-pointer ${
                          currentWorkspace?.id === workspace.id
                            ? "ring-2 ring-primary ring-offset-2"
                            : ""
                        }`}
                        onClick={() => handleSwitchWorkspace(workspace)}
                      >
                        <CardHeader className="pb-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <Avatar className={`h-12 w-12 ${getWorkspaceColor(workspace.name)}`}>
                                <AvatarFallback className="text-white font-semibold">
                                  {getWorkspaceInitials(workspace.name)}
                                </AvatarFallback>
                              </Avatar>
                              <div className="min-w-0 flex-1">
                                <h3 className="font-semibold text-base truncate">
                                  {workspace.name}
                                </h3>
                                <p className="text-sm text-muted-foreground line-clamp-2">
                                  {workspace.description || "No description"}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {currentWorkspace?.id === workspace.id && (
                                <CheckCircle2 className="h-4 w-4 text-primary" />
                              )}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSwitchWorkspace(workspace);
                                    }}
                                  >
                                    Switch to workspace
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditWorkspace(workspace);
                                    }}
                                  >
                                    <Settings className="h-4 w-4 mr-2" />
                                    Edit workspace
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleManageUsers(workspace);
                                    }}
                                  >
                                    <Users className="h-4 w-4 mr-2" />
                                    Manage users
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteWorkspace(workspace);
                                    }}
                                    className="text-destructive focus:text-destructive"
                                  >
                                    <Archive className="h-4 w-4 mr-2" />
                                    Delete workspace
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center text-muted-foreground">
                                <Users className="h-4 w-4 mr-1" />
                                <span>{workspace.members} members</span>
                              </div>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {getUserRole()}
                            </Badge>
                          </div>
                          <div className="mt-3 text-xs text-muted-foreground">
                            Last activity: {getLastActivity()}
                          </div>
                          {switchingWorkspace === workspace.id && (
                            <div className="mt-3 flex items-center text-xs text-primary">
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                              Switching...
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* No search results */}
                  {filteredWorkspaces.length === 0 && searchQuery && (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Search className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No workspaces found</h3>
                      <p className="text-muted-foreground">
                        Try adjusting your search terms or create a new workspace
                      </p>
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </>
      )}

      {/* Create Workspace Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Workspace</DialogTitle>
            <DialogDescription>
              Set up a new workspace for your team or project
            </DialogDescription>
          </DialogHeader>
          <WorkspaceForm
            onClose={() => setIsCreateDialogOpen(false)}
            onWorkspaceCreated={(newWorkspace) => {
              setIsCreateDialogOpen(false);
              toast({
                title: "Workspace created",
                description: "Your new workspace has been created successfully",
              });
              // Optionally switch to the new workspace
              if (newWorkspace) {
                setCurrentWorkspace(newWorkspace);
              }
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Workspace Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Workspace</DialogTitle>
            <DialogDescription>
              Update workspace details and settings
            </DialogDescription>
          </DialogHeader>
          <WorkspaceForm
            workspace={selectedWorkspace || undefined}
            onClose={() => {
              setIsEditDialogOpen(false);
              setSelectedWorkspace(null);
            }}
            onWorkspaceUpdated={(_id, _updatedData) => {
              setIsEditDialogOpen(false);
              setSelectedWorkspace(null);
              toast({
                title: "Workspace updated",
                description: "Workspace has been updated successfully",
              });
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Manage Users Dialog */}
      <Dialog open={isManageUsersOpen} onOpenChange={setIsManageUsersOpen}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Manage Users - {selectedWorkspace?.name}</DialogTitle>
            <DialogDescription>
              Add, remove, and manage user roles for this workspace
            </DialogDescription>
          </DialogHeader>
          {selectedWorkspace && (
            <UserAccessManager
              workspace={selectedWorkspace}
              onClose={() => setIsManageUsersOpen(false)}
              onUsersUpdated={() => {
                toast({
                  title: "Users updated",
                  description: "Workspace users have been updated successfully",
                });
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WorkspacePage;
