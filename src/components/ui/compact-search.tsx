import React, { useState, useRef, useEffect } from "react";
import { Search, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { cn } from "@/lib/utils";
import { SearchQuery } from "./enhanced-search";

export interface CompactSearchProps {
  // Search props
  searchValue: SearchQuery;
  onSearchChange: (query: SearchQuery) => void;
  searchPlaceholder?: string;
  recentSearches?: string[];
  suggestions?: string[];

  // UI props
  className?: string;

  // Results info
  totalResults?: number;
  showResultsCount?: boolean;
}

const CompactSearch: React.FC<CompactSearchProps> = ({
  searchValue,
  onSearchChange,
  searchPlaceholder = "Search...",
  recentSearches = [],
  suggestions = [],
  className,
  totalResults,
  showResultsCount = true,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      // Small delay to ensure the animation has started
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isExpanded]);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
        setShowSuggestions(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isExpanded]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsExpanded(false);
        setShowSuggestions(false);
      }
      // Ctrl/Cmd + K to open search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k' && !isExpanded) {
        event.preventDefault();
        setIsExpanded(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isExpanded]);

  const hasActiveSearch = searchValue.global?.trim() || Object.keys(searchValue.fields || {}).length > 0;

  const handleSearchToggle = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setShowSuggestions(false);
    }
  };

  const handleClearSearch = () => {
    onSearchChange({ global: '', fields: {} });
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onSearchChange({ global: value, fields: searchValue.fields || {} });
    setShowSuggestions(value.length > 0 && (suggestions.length > 0 || recentSearches.length > 0));
  };

  const handleInputFocus = () => {
    if (searchValue.global || recentSearches.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onSearchChange({ global: suggestion, fields: searchValue.fields || {} });
    if (inputRef.current) {
      inputRef.current.value = suggestion;
    }
    setShowSuggestions(false);
  };

  return (
    <div ref={containerRef} className={cn("flex items-center gap-2", className)}>
      {/* Animated search container */}
      <div
        className={cn(
          "relative flex items-center transition-all duration-300 ease-out overflow-hidden",
          isExpanded ? "w-48 sm:w-64 md:w-80" : "w-auto"
        )}
      >
        {!isExpanded ? (
          // Compact search icon button
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "flex items-center gap-2 transition-all duration-300 ease-out",
              hasActiveSearch && "bg-primary/10 border-primary/20"
            )}
            onClick={handleSearchToggle}
          >
            <Search className="h-4 w-4" />
            {hasActiveSearch && <span className="text-xs">Active</span>}
          </Button>
        ) : (
          // Expanded search input
          <div className="relative w-full">
            <div className="relative flex items-center">
              <Search className="absolute left-3 h-4 w-4 text-muted-foreground pointer-events-none" />
              <Input
                ref={inputRef}
                type="text"
                placeholder={searchPlaceholder}
                value={searchValue.global || ''}
                onChange={handleInputChange}
                onFocus={handleInputFocus}
                className={cn(
                  "pl-10 pr-20 h-8 text-sm transition-all duration-300 ease-out focus:ring-2 focus:ring-primary/20 focus:border-primary/40",
                  hasActiveSearch && "bg-primary/5 border-primary/20"
                )}
              />
              <div className="absolute right-1 flex items-center gap-1">
                {hasActiveSearch && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearSearch}
                    className="h-6 w-6 p-0 hover:bg-muted"
                    title="Clear search"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSearchToggle}
                  className="h-6 w-6 p-0 hover:bg-muted"
                  title="Close search"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {/* Search suggestions dropdown */}
            {showSuggestions && (suggestions.length > 0 || recentSearches.length > 0) && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50 max-h-60 overflow-y-auto animate-in fade-in-0 slide-in-from-top-1 duration-200">
                {recentSearches.length > 0 && (
                  <div className="p-2">
                    <div className="text-xs font-medium text-muted-foreground mb-1">Recent searches</div>
                    {recentSearches.slice(0, 3).map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(search)}
                        className="w-full text-left px-2 py-1 text-sm hover:bg-muted rounded text-muted-foreground"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                )}
                {suggestions.length > 0 && (
                  <div className="p-2 border-t">
                    <div className="text-xs font-medium text-muted-foreground mb-1">Suggestions</div>
                    {suggestions.slice(0, 5).map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full text-left px-2 py-1 text-sm hover:bg-muted rounded"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Results counter */}
            {showResultsCount && totalResults !== undefined && isExpanded && (
              <div className="absolute top-full left-0 mt-1 text-xs text-muted-foreground">
                {totalResults} {totalResults === 1 ? 'result' : 'results'} found
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CompactSearch;
