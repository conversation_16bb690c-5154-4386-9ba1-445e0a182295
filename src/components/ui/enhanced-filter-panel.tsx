import React, { useState } from "react";
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronRight, 
  Calendar,
  User,
  Tag,
  Settings,
  Bookmark
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface FilterGroup {
  key: string;
  label: string;
  type: "select" | "multiselect" | "date-range" | "text" | "checkbox";
  options?: FilterOption[];
  placeholder?: string;
  icon?: React.ReactNode;
  defaultOpen?: boolean;
}

export interface FilterValues {
  [key: string]: any;
}

export interface EnhancedFilterPanelProps {
  filters: FilterGroup[];
  values: FilterValues;
  onChange: (values: FilterValues) => void;
  onClear?: () => void;
  onSaveFilter?: (name: string, values: FilterValues) => void;
  savedFilters?: { name: string; values: FilterValues }[];
  className?: string;
  collapsible?: boolean;
  showFilterCount?: boolean;
}

const EnhancedFilterPanel: React.FC<EnhancedFilterPanelProps> = ({
  filters,
  values,
  onChange,
  onClear,
  onSaveFilter,
  savedFilters = [],
  className,
  collapsible = true,
  showFilterCount = true,
}) => {
  const [openGroups, setOpenGroups] = useState<Set<string>>(
    new Set(filters.filter(f => f.defaultOpen !== false).map(f => f.key))
  );
  const [isMainCollapsed, setIsMainCollapsed] = useState(false);

  // Toggle group open/closed
  const toggleGroup = (groupKey: string) => {
    const newOpenGroups = new Set(openGroups);
    if (newOpenGroups.has(groupKey)) {
      newOpenGroups.delete(groupKey);
    } else {
      newOpenGroups.add(groupKey);
    }
    setOpenGroups(newOpenGroups);
  };

  // Update filter value
  const updateFilter = (key: string, value: any) => {
    onChange({
      ...values,
      [key]: value,
    });
  };

  // Get active filter count
  const activeFilterCount = Object.keys(values).filter(key => {
    const value = values[key];
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === "object" && value !== null) {
      return Object.keys(value).some(k => value[k]);
    }
    return value !== undefined && value !== null && value !== "";
  }).length;

  // Render filter input based on type
  const renderFilterInput = (filter: FilterGroup) => {
    const value = values[filter.key];

    switch (filter.type) {
      case "text":
        return (
          <Input
            placeholder={filter.placeholder}
            value={value || ""}
            onChange={(e) => updateFilter(filter.key, e.target.value)}
            className="h-8"
          />
        );

      case "select":
        return (
          <Select
            value={value || ""}
            onValueChange={(newValue) => updateFilter(filter.key, newValue)}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder={filter.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {filter.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center justify-between w-full">
                    <span>{option.label}</span>
                    {option.count !== undefined && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {option.count}
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "multiselect":
        const selectedValues = Array.isArray(value) ? value : [];
        return (
          <div className="space-y-2">
            {filter.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`${filter.key}-${option.value}`}
                  checked={selectedValues.includes(option.value)}
                  onCheckedChange={(checked) => {
                    const newValues = checked
                      ? [...selectedValues, option.value]
                      : selectedValues.filter(v => v !== option.value);
                    updateFilter(filter.key, newValues);
                  }}
                />
                <Label
                  htmlFor={`${filter.key}-${option.value}`}
                  className="text-sm font-normal flex-1 cursor-pointer"
                >
                  {option.label}
                  {option.count !== undefined && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {option.count}
                    </Badge>
                  )}
                </Label>
              </div>
            ))}
          </div>
        );

      case "date-range":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full h-8 justify-start text-left font-normal"
              >
                <Calendar className="mr-2 h-3 w-3" />
                {value?.from ? (
                  value.to ? (
                    <>
                      {format(value.from, "LLL dd")} - {format(value.to, "LLL dd")}
                    </>
                  ) : (
                    format(value.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                initialFocus
                mode="range"
                defaultMonth={value?.from}
                selected={value}
                onSelect={(range) => updateFilter(filter.key, range)}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        );

      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={filter.key}
              checked={!!value}
              onCheckedChange={(checked) => updateFilter(filter.key, checked)}
            />
            <Label htmlFor={filter.key} className="text-sm font-normal cursor-pointer">
              {filter.label}
            </Label>
          </div>
        );

      default:
        return null;
    }
  };

  const FilterContent = () => (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <span className="font-medium">Filters</span>
          {showFilterCount && activeFilterCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeFilterCount}
            </Badge>
          )}
        </div>
        {onClear && activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClear}
            className="h-6 text-xs"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div className="space-y-2">
          <Label className="text-xs font-medium text-muted-foreground">
            SAVED FILTERS
          </Label>
          <div className="space-y-1">
            {savedFilters.map((savedFilter, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={() => onChange(savedFilter.values)}
                className="w-full justify-start h-7 text-xs"
              >
                <Bookmark className="mr-2 h-3 w-3" />
                {savedFilter.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Filter Groups */}
      <div className="space-y-3">
        {filters.map((filter) => (
          <Collapsible
            key={filter.key}
            open={openGroups.has(filter.key)}
            onOpenChange={() => toggleGroup(filter.key)}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-between h-8 px-2 font-normal"
              >
                <div className="flex items-center gap-2">
                  {filter.icon}
                  <span className="text-sm">{filter.label}</span>
                  {values[filter.key] && (
                    <Badge variant="secondary" className="text-xs">
                      Active
                    </Badge>
                  )}
                </div>
                {openGroups.has(filter.key) ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="px-2 pt-2">
              {renderFilterInput(filter)}
            </CollapsibleContent>
          </Collapsible>
        ))}
      </div>
    </div>
  );

  if (collapsible) {
    return (
      <Collapsible
        open={!isMainCollapsed}
        onOpenChange={setIsMainCollapsed}
        className={cn("border rounded-lg", className)}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-3 h-auto"
          >
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span className="font-medium">Filters</span>
              {showFilterCount && activeFilterCount > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            {isMainCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="p-3 pt-0">
          <FilterContent />
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <div className={cn("border rounded-lg p-3", className)}>
      <FilterContent />
    </div>
  );
};

export default EnhancedFilterPanel;

// Filter Chips Component
export interface FilterChipsProps {
  filters: FilterGroup[];
  values: FilterValues;
  onChange: (values: FilterValues) => void;
  onClear?: () => void;
  className?: string;
}

export const FilterChips: React.FC<FilterChipsProps> = ({
  filters,
  values,
  onChange,
  onClear,
  className,
}) => {
  const activeFilters = Object.entries(values).filter(([key, value]) => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === "object" && value !== null) {
      return Object.keys(value).some(k => value[k]);
    }
    return value !== undefined && value !== null && value !== "";
  });

  if (activeFilters.length === 0) return null;

  const removeFilter = (key: string) => {
    const newValues = { ...values };
    delete newValues[key];
    onChange(newValues);
  };

  const getFilterLabel = (key: string, value: any) => {
    const filter = filters.find(f => f.key === key);
    const filterLabel = filter?.label || key;

    if (Array.isArray(value)) {
      return `${filterLabel}: ${value.length} selected`;
    }

    if (typeof value === "object" && value !== null && (value.from || value.to)) {
      if (value.from && value.to) {
        return `${filterLabel}: ${format(value.from, "MMM dd")} - ${format(value.to, "MMM dd")}`;
      }
      return `${filterLabel}: ${value.from ? format(value.from, "MMM dd") : "Until " + format(value.to, "MMM dd")}`;
    }

    if (typeof value === "boolean") {
      return filterLabel;
    }

    return `${filterLabel}: ${value}`;
  };

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      {activeFilters.map(([key, value]) => (
        <Badge
          key={key}
          variant="secondary"
          className="flex items-center gap-1 pr-1"
        >
          <span className="text-xs">{getFilterLabel(key, value)}</span>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => removeFilter(key)}
            className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      ))}

      {onClear && activeFilters.length > 1 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClear}
          className="h-6 text-xs text-muted-foreground hover:text-foreground"
        >
          Clear all
        </Button>
      )}
    </div>
  );
};
