import React from "react";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface LoadingProps {
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  variant?: "default" | "primary" | "secondary" | "ghost";
  className?: string;
  text?: string;
  fullPage?: boolean;
}

const sizeClasses = {
  xs: "h-3 w-3",
  sm: "h-4 w-4",
  md: "h-6 w-6",
  lg: "h-8 w-8",
  xl: "h-12 w-12",
};

const variantClasses = {
  default: "text-muted-foreground",
  primary: "text-primary",
  secondary: "text-secondary",
  ghost: "text-muted-foreground/50",
};

export const Loading: React.FC<LoadingProps> = ({
  size = "md",
  variant = "default",
  className,
  text,
  fullPage = false,
}) => {
  const loader = (
    <div className={cn("flex items-center gap-2", className)}>
      <Loader2 className={cn("animate-spin", sizeClasses[size], variantClasses[variant])} />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  );

  if (fullPage) {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-background border rounded-lg shadow-lg p-6 max-w-md w-full mx-4">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className={cn("animate-spin", sizeClasses.xl, variantClasses[variant])} />
            {text && <p className="text-sm text-center">{text}</p>}
          </div>
        </div>
      </div>
    );
  }

  return loader;
};

interface LoadingOverlayProps {
  active: boolean;
  text?: string;
  spinnerSize?: LoadingProps["size"];
  className?: string;
  children: React.ReactNode;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  active,
  text,
  spinnerSize = "md",
  className,
  children,
}) => {
  return (
    <div className={cn("relative", className)}>
      {children}

      {active && (
        <div className="absolute inset-0 bg-background/60 backdrop-blur-[1px] flex items-center justify-center rounded-md z-10">
          <Loading size={spinnerSize} text={text} />
        </div>
      )}
    </div>
  );
};

// Enhanced loading overlay with better visual feedback
export const SimpleLoadingOverlay: React.FC<{
  text?: string;
  spinnerSize?: LoadingProps["size"];
  className?: string;
  variant?: "default" | "subtle" | "blur";
}> = ({
  text = "Loading...",
  spinnerSize = "md",
  className,
  variant = "default",
}) => {
  const overlayClasses = {
    default: "absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center rounded-md z-10",
    subtle: "absolute inset-0 bg-background/60 flex items-center justify-center rounded-md z-10",
    blur: "absolute inset-0 bg-background/90 backdrop-blur-md flex items-center justify-center rounded-md z-10",
  };

  return (
    <div className={cn(overlayClasses[variant], className)}>
      <div className="flex flex-col items-center space-y-3 p-4">
        <Loading size={spinnerSize} />
        {text && (
          <p className="text-sm text-muted-foreground animate-pulse">
            {text}
          </p>
        )}
      </div>
    </div>
  );
};

interface LoadingButtonProps {
  loading: boolean;
  children: React.ReactNode;
  spinnerSize?: LoadingProps["size"];
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  type?: "button" | "submit" | "reset";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading,
  children,
  spinnerSize = "sm",
  className,
  disabled,
  onClick,
  type = "button",
  variant = "default",
  size = "default",
}) => {
  return (
    <Button
      type={type}
      variant={variant}
      size={size}
      className={className}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading ? (
        <>
          <Loader2 className={cn("animate-spin mr-2", sizeClasses[spinnerSize])} />
          {children}
        </>
      ) : (
        children
      )}
    </Button>
  );
};

// Enhanced loading states for different scenarios
export const LoadingCard: React.FC<{
  title?: string;
  description?: string;
  className?: string;
}> = ({ title = "Loading", description, className }) => (
  <div className={cn("p-5 border border-border/50 rounded-xl space-y-4", className)}>
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <Loading size="sm" />
        <h3 className="heading-card">{title}</h3>
      </div>
      {description && (
        <p className="caption-text">{description}</p>
      )}
    </div>
  </div>
);

export const LoadingPage: React.FC<{
  title?: string;
  description?: string;
  className?: string;
}> = ({
  title = "Loading",
  description = "Please wait while we load your content...",
  className
}) => (
  <div className={cn("page-container flex items-center justify-center", className)}>
    <div className="text-center space-y-4 max-w-md">
      <Loading size="lg" />
      <div className="space-y-2">
        <h2 className="heading-secondary">{title}</h2>
        <p className="body-text-secondary">{description}</p>
      </div>
    </div>
  </div>
);

export const LoadingInline: React.FC<{
  text?: string;
  size?: LoadingProps["size"];
  className?: string;
}> = ({ text = "Loading...", size = "sm", className }) => (
  <div className={cn("flex items-center space-x-2", className)}>
    <Loading size={size} />
    <span className="text-sm text-muted-foreground">{text}</span>
  </div>
);


