import React, { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  Check,
  Clock,
  Download,
  Eye,
  FileEdit,
  FileText,
  History,
  Pencil,
  Search,
  Trash,
  Upload,
  User,
  UserPlus,
  X,
} from "lucide-react";

// Types
interface ActivityItem {
  id: string;
  type: "create" | "update" | "delete" | "approve" | "reject" | "share" | "upload" | "download" | "comment" | "user";
  entityType: "contract" | "template" | "clause" | "user" | "role" | "approval" | "comment";
  entityId: string;
  entityName: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  timestamp: string;
  details?: string;
  metadata?: Record<string, unknown>;
}

interface ActivityHistoryProps {
  activities?: ActivityItem[];
  onViewEntity?: (entityType: string, entityId: string) => void;
}

const ActivityHistory: React.FC<ActivityHistoryProps> = ({
  activities = [],
  onViewEntity,
}) => {
  // State
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("all");
  const [selectedUser, setSelectedUser] = useState<string>("all");
  const [selectedEntityType, setSelectedEntityType] = useState<string>("all");
  const [selectedActionType, setSelectedActionType] = useState<string>("all");
  const [dateRange, setDateRange] = useState<string>("all");

  // Filter activities based on filters
  const filteredActivities = activities.filter(activity => {
    // Filter by search term
    const matchesSearch =
      searchTerm === "" ||
      activity.entityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (activity.details && activity.details.toLowerCase().includes(searchTerm.toLowerCase()));

    // Filter by tab
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "contracts" && activity.entityType === "contract") ||
      (activeTab === "templates" && activity.entityType === "template") ||
      (activeTab === "approvals" && (activity.type === "approve" || activity.type === "reject"));

    // Filter by user
    const matchesUser =
      selectedUser === "all" ||
      activity.user.id === selectedUser;

    // Filter by entity type
    const matchesEntityType =
      selectedEntityType === "all" ||
      activity.entityType === selectedEntityType;

    // Filter by action type
    const matchesActionType =
      selectedActionType === "all" ||
      activity.type === selectedActionType;

    // Filter by date range
    const matchesDateRange = true; // Implement date range filtering if needed

    return matchesSearch && matchesTab && matchesUser && matchesEntityType && matchesActionType && matchesDateRange;
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? "minute" : "minutes"} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? "hour" : "hours"} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} ${diffInDays === 1 ? "day" : "days"} ago`;
    } else {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  };

  // Get activity icon
  const getActivityIcon = (type: ActivityItem["type"]) => {
    switch (type) {
      case "create":
        return <FileText className="h-5 w-5 text-green-500" />;
      case "update":
        return <FileEdit className="h-5 w-5 text-blue-500" />;
      case "delete":
        return <Trash className="h-5 w-5 text-red-500" />;
      case "approve":
        return <Check className="h-5 w-5 text-green-500" />;
      case "reject":
        return <X className="h-5 w-5 text-red-500" />;
      case "share":
        return <User className="h-5 w-5 text-purple-500" />;
      case "upload":
        return <Upload className="h-5 w-5 text-blue-500" />;
      case "download":
        return <Download className="h-5 w-5 text-blue-500" />;
      case "comment":
        return <Pencil className="h-5 w-5 text-amber-500" />;
      case "user":
        return <UserPlus className="h-5 w-5 text-green-500" />;
      default:
        return <History className="h-5 w-5 text-muted-foreground" />;
    }
  };

  // Get activity description
  const getActivityDescription = (activity: ActivityItem) => {
    const { type, entityType, entityName } = activity;

    switch (type) {
      case "create":
        return `created ${entityType} "${entityName}"`;
      case "update":
        return `updated ${entityType} "${entityName}"`;
      case "delete":
        return `deleted ${entityType} "${entityName}"`;
      case "approve":
        return `approved ${entityType} "${entityName}"`;
      case "reject":
        return `rejected ${entityType} "${entityName}"`;
      case "share":
        return `shared ${entityType} "${entityName}"`;
      case "upload":
        return `uploaded ${entityType} "${entityName}"`;
      case "download":
        return `downloaded ${entityType} "${entityName}"`;
      case "comment":
        return `commented on ${entityType} "${entityName}"`;
      case "user":
        return `added user "${entityName}"`;
      default:
        return `performed action on ${entityType} "${entityName}"`;
    }
  };

  // Get unique users from activities
  const uniqueUsers = Array.from(
    new Set(activities.map(activity => activity.user.id))
  ).map(userId => {
    const activity = activities.find(a => a.user.id === userId);
    return {
      id: userId,
      name: activity?.user.name || "",
      avatar: activity?.user.avatar,
      initials: activity?.user.initials || "",
    };
  });

  // Get unique entity types from activities
  const uniqueEntityTypes = Array.from(
    new Set(activities.map(activity => activity.entityType))
  );

  // Get unique action types from activities
  const uniqueActionTypes = Array.from(
    new Set(activities.map(activity => activity.type))
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-end">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search activities..."
              className="pl-8 h-9 w-[200px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" size="sm" className="h-9">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="all">All Activity</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="approvals">Approvals</TabsTrigger>
        </TabsList>

        <div className="flex flex-wrap gap-3 mt-4">
          <Select value={selectedUser} onValueChange={setSelectedUser}>
            <SelectTrigger className="w-[180px] h-9">
              <SelectValue placeholder="Filter by user" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Users</SelectItem>
              {uniqueUsers.map(user => (
                <SelectItem key={user.id} value={user.id}>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-5 w-5">
                      {user.avatar ? (
                        <AvatarImage src={user.avatar} alt={user.name} />
                      ) : (
                        <AvatarFallback className="text-[10px]">
                          {user.initials}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span>{user.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedEntityType} onValueChange={setSelectedEntityType}>
            <SelectTrigger className="w-[180px] h-9">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {uniqueEntityTypes.map(type => (
                <SelectItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedActionType} onValueChange={setSelectedActionType}>
            <SelectTrigger className="w-[180px] h-9">
              <SelectValue placeholder="Filter by action" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              {uniqueActionTypes.map(type => (
                <SelectItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px] h-9">
              <SelectValue placeholder="Date range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="ghost"
            size="sm"
            className="h-9"
            onClick={() => {
              setSearchTerm("");
              setSelectedUser("all");
              setSelectedEntityType("all");
              setSelectedActionType("all");
              setDateRange("all");
            }}
          >
            <X className="h-4 w-4 mr-2" />
            Clear Filters
          </Button>
        </div>

        <TabsContent value={activeTab} className="mt-4">
          <Card>
            <CardContent className="p-0">
              {filteredActivities.length > 0 ? (
                <ScrollArea className="h-[600px]">
                  <div className="divide-y">
                    {filteredActivities.map((activity) => (
                      <div
                        key={activity.id}
                        className="p-4 hover:bg-muted/50"
                      >
                        <div className="flex items-start gap-4">
                          <div className="mt-0.5">
                            <Avatar className="h-9 w-9">
                              {activity.user.avatar ? (
                                <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                              ) : (
                                <AvatarFallback>
                                  {activity.user.initials}
                                </AvatarFallback>
                              )}
                            </Avatar>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div>
                                <div className="flex items-center gap-2">
                                  <h3 className="font-medium">{activity.user.name}</h3>
                                  <span className="text-muted-foreground">{getActivityDescription(activity)}</span>
                                </div>
                                {activity.details && (
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {activity.details}
                                  </p>
                                )}
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="text-xs text-muted-foreground flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {formatDate(activity.timestamp)}
                                </div>
                                <div className="flex items-center">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={() => onViewEntity && onViewEntity(activity.entityType, activity.entityId)}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                            <div className="mt-2 flex items-center gap-2">
                              <Badge variant="outline" className="flex items-center gap-1">
                                {getActivityIcon(activity.type)}
                                <span className="capitalize">{activity.type}</span>
                              </Badge>
                              <Badge variant="outline" className="capitalize">
                                {activity.entityType}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <History className="h-12 w-12 text-muted-foreground mb-3" />
                  <h3 className="text-lg font-medium mb-1">No activities found</h3>
                  <p className="text-sm text-muted-foreground">
                    {searchTerm || selectedUser !== "all" || selectedEntityType !== "all" || selectedActionType !== "all" || dateRange !== "all"
                      ? "Try adjusting your filters"
                      : "No activities have been recorded yet"}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export type { ActivityItem };
export default ActivityHistory;
