import React, { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer';
import { 
  Brain, 
  Zap, 
  AlertTriangle, 
  TrendingUp, 
  Shield, 
  FileText, 
  ChevronUp,
  ChevronDown,
  Maximize2,
  Minimize2,
  MoreVertical,
  Share,
  Download,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@/hooks/use-media-query';

interface MobileAIInterfaceProps {
  contractId: string;
  analysisData?: any;
  onAnalyze?: () => void;
  onShare?: () => void;
  onExport?: () => void;
  isAnalyzing?: boolean;
  className?: string;
}

export const MobileAIInterface: React.FC<MobileAIInterfaceProps> = ({
  contractId,
  analysisData,
  onAnalyze,
  onShare,
  onExport,
  isAnalyzing = false,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'risks' | 'insights' | 'recommendations'>('overview');
  const [showFullScreen, setShowFullScreen] = useState(false);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const containerRef = useRef<HTMLDivElement>(null);

  const analysisResults = analysisData || {
    risk_score: 0.65,
    contract_type: 'Service Agreement',
    key_clauses: ['Payment Terms', 'Termination', 'Liability'],
    risks: [
      { type: 'high', title: 'Unlimited Liability', description: 'Contract contains unlimited liability clause' },
      { type: 'medium', title: 'Auto-renewal', description: 'Automatic renewal without notice period' }
    ],
    opportunities: [
      { type: 'low', title: 'Cost Savings', description: 'Potential for volume discounts' }
    ],
    recommendations: [
      'Add liability cap of $100,000',
      'Include 30-day termination notice',
      'Negotiate volume pricing tiers'
    ]
  };

  const getRiskColor = (score: number) => {
    if (score >= 0.8) return 'text-red-600 bg-red-100';
    if (score >= 0.6) return 'text-orange-600 bg-orange-100';
    if (score >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const TabButton = ({ id, label, icon, active, onClick }: any) => (
    <button
      onClick={() => onClick(id)}
      className={cn(
        "flex-1 flex flex-col items-center justify-center p-3 text-xs font-medium transition-colors",
        active 
          ? "text-blue-600 bg-blue-50 border-t-2 border-blue-600" 
          : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
      )}
    >
      {icon}
      <span className="mt-1">{label}</span>
    </button>
  );

  const OverviewContent = () => (
    <div className="space-y-4">
      {/* Risk Score */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Risk Score</span>
            <Badge className={getRiskColor(analysisResults.risk_score)}>
              {Math.round(analysisResults.risk_score * 100)}%
            </Badge>
          </div>
          <Progress value={analysisResults.risk_score * 100} className="h-2" />
          <p className="text-xs text-gray-600 mt-2">
            {analysisResults.risk_score >= 0.7 ? 'High risk contract requiring review' : 
             analysisResults.risk_score >= 0.4 ? 'Medium risk with some concerns' : 
             'Low risk contract'}
          </p>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-3">
        <Card>
          <CardContent className="p-3 text-center">
            <div className="text-lg font-bold text-gray-900">{analysisResults.risks?.length || 0}</div>
            <div className="text-xs text-gray-600">Risks Found</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-3 text-center">
            <div className="text-lg font-bold text-gray-900">{analysisResults.key_clauses?.length || 0}</div>
            <div className="text-xs text-gray-600">Key Clauses</div>
          </CardContent>
        </Card>
      </div>

      {/* Contract Type */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium">Contract Type</span>
          </div>
          <p className="text-lg font-semibold text-gray-900 mt-1">
            {analysisResults.contract_type || 'Unknown'}
          </p>
        </CardContent>
      </Card>
    </div>
  );

  const RisksContent = () => (
    <div className="space-y-3">
      {analysisResults.risks?.map((risk: any, index: number) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <AlertTriangle className={cn(
                    "h-4 w-4",
                    risk.type === 'high' ? 'text-red-500' :
                    risk.type === 'medium' ? 'text-orange-500' : 'text-yellow-500'
                  )} />
                  <span className="font-medium text-sm">{risk.title}</span>
                </div>
                <p className="text-xs text-gray-600">{risk.description}</p>
              </div>
              <Badge variant="outline" className={cn(
                "text-xs",
                risk.type === 'high' ? 'border-red-200 text-red-700' :
                risk.type === 'medium' ? 'border-orange-200 text-orange-700' : 'border-yellow-200 text-yellow-700'
              )}>
                {risk.type}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )) || (
        <div className="text-center py-8 text-gray-500">
          <Shield className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No risks identified</p>
        </div>
      )}
    </div>
  );

  const InsightsContent = () => (
    <div className="space-y-3">
      {analysisResults.opportunities?.map((opportunity: any, index: number) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-start space-x-2">
              <TrendingUp className="h-4 w-4 text-green-500 mt-0.5" />
              <div className="flex-1">
                <span className="font-medium text-sm">{opportunity.title}</span>
                <p className="text-xs text-gray-600 mt-1">{opportunity.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )) || (
        <div className="text-center py-8 text-gray-500">
          <TrendingUp className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No opportunities identified</p>
        </div>
      )}
    </div>
  );

  const RecommendationsContent = () => (
    <div className="space-y-3">
      {analysisResults.recommendations?.map((recommendation: string, index: number) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <p className="text-sm text-gray-700">{recommendation}</p>
            </div>
          </CardContent>
        </Card>
      )) || (
        <div className="text-center py-8 text-gray-500">
          <Brain className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No recommendations available</p>
        </div>
      )}
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return <OverviewContent />;
      case 'risks': return <RisksContent />;
      case 'insights': return <InsightsContent />;
      case 'recommendations': return <RecommendationsContent />;
      default: return <OverviewContent />;
    }
  };

  const MobileInterface = () => (
    <div className={cn("fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-50", className)}>
      {/* Collapsed State */}
      {!isExpanded && (
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="h-6 w-6 text-blue-600" />
              <div>
                <h3 className="font-medium text-sm">AI Analysis</h3>
                <p className="text-xs text-gray-600">
                  {isAnalyzing ? 'Analyzing...' : 'Ready to analyze'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {!isAnalyzing && analysisData && (
                <Badge className={getRiskColor(analysisResults.risk_score)}>
                  {Math.round(analysisResults.risk_score * 100)}%
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(true)}
              >
                <ChevronUp className="h-4 w-4" />
              </Button>
            </div>
          </div>
          {isAnalyzing && (
            <Progress value={65} className="h-1 mt-2" />
          )}
        </div>
      )}

      {/* Expanded State */}
      {isExpanded && (
        <div className="h-96 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium">AI Analysis</h3>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" onClick={onShare}>
                  <Share className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={onExport}>
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(false)}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {isAnalyzing ? (
              <div className="text-center py-8">
                <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                <p className="text-sm text-gray-600">Analyzing contract...</p>
                <Progress value={65} className="mt-4" />
              </div>
            ) : analysisData ? (
              renderContent()
            ) : (
              <div className="text-center py-8">
                <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="font-medium text-gray-900 mb-2">Ready to Analyze</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Get AI-powered insights for this contract
                </p>
                <Button onClick={onAnalyze}>
                  <Zap className="h-4 w-4 mr-2" />
                  Start Analysis
                </Button>
              </div>
            )}
          </div>

          {/* Tab Navigation */}
          {analysisData && !isAnalyzing && (
            <div className="border-t bg-gray-50">
              <div className="flex">
                <TabButton
                  id="overview"
                  label="Overview"
                  icon={<FileText className="h-4 w-4" />}
                  active={activeTab === 'overview'}
                  onClick={setActiveTab}
                />
                <TabButton
                  id="risks"
                  label="Risks"
                  icon={<AlertTriangle className="h-4 w-4" />}
                  active={activeTab === 'risks'}
                  onClick={setActiveTab}
                />
                <TabButton
                  id="insights"
                  label="Insights"
                  icon={<TrendingUp className="h-4 w-4" />}
                  active={activeTab === 'insights'}
                  onClick={setActiveTab}
                />
                <TabButton
                  id="recommendations"
                  label="Actions"
                  icon={<Brain className="h-4 w-4" />}
                  active={activeTab === 'recommendations'}
                  onClick={setActiveTab}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );

  // Desktop version uses a card layout
  if (isDesktop) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-blue-600" />
              <span>AI Analysis</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onShare}>
                <Share className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={onExport}>
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isAnalyzing ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-sm text-gray-600">Analyzing contract...</p>
              <Progress value={65} className="mt-4" />
            </div>
          ) : analysisData ? (
            <OverviewContent />
          ) : (
            <div className="text-center py-8">
              <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">Ready to Analyze</h3>
              <p className="text-sm text-gray-600 mb-4">
                Get AI-powered insights for this contract
              </p>
              <Button onClick={onAnalyze}>
                <Zap className="h-4 w-4 mr-2" />
                Start Analysis
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Mobile version
  return <MobileInterface />;
};
