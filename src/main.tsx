import React from "react";
import <PERSON>actD<PERSON> from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import App from "./App";
import "./index.css";
import ErrorBoundary from "./components/error-boundary";
import { <PERSON><PERSON>rovider } from "./lib/clerk-provider";

// Add error logging
console.log("🚀 Starting Averum Contracts application...");

try {
  const rootElement = document.getElementById("root");
  if (!rootElement) {
    throw new Error("Root element not found");
  }

  console.log("✅ Root element found, creating React root...");

  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <ErrorBoundary>
        <ClerkProvider>
          <BrowserRouter>
            <App />
          </BrowserRouter>
        </ClerkProvider>
      </ErrorBoundary>
    </React.StrictMode>,
  );

  console.log("✅ React app rendered successfully!");
} catch (error) {
  console.error("❌ Failed to render React app:", error);

  // Fallback error display
  const rootElement = document.getElementById("root");
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; padding: 2rem; text-align: center; font-family: system-ui, sans-serif;">
        <h1 style="color: #dc2626; margin-bottom: 1rem;">Application Error</h1>
        <p style="color: #374151; margin-bottom: 1rem;">Failed to load the application. Please check the console for details.</p>
        <button onclick="window.location.reload()" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
          Reload Page
        </button>
        <pre style="margin-top: 1rem; padding: 1rem; background: #f3f4f6; border-radius: 0.375rem; font-size: 0.875rem; text-align: left; overflow: auto; max-width: 100%;">${error}</pre>
      </div>
    `;
  }
}
