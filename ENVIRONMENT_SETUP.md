# Environment Setup Guide

## 🔒 Security Notice

This project uses environment variables to store sensitive configuration data. **Never commit actual `.env` files to version control.**

## 📋 Setup Instructions

### Frontend Environment Setup

1. Copy the example file:
   ```bash
   cp .env.example .env
   ```

2. Update the values in `.env`:
   ```bash
   # Supabase Configuration
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_actual_anon_key

   # Clerk Authentication
   VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_actual_key

   # API Configuration
   VITE_API_BASE_URL=http://localhost:8000/api

   # Document Engine Configuration
   VITE_USE_NEW_DOCUMENT_ENGINE=true
   VITE_WS_URL=ws://localhost:8000/ws

   # Environment
   VITE_ENVIRONMENT=development
   ```

### Backend Environment Setup

1. Copy the example file:
   ```bash
   cp backend/.env.example backend/.env
   ```

2. Update the values in `backend/.env`:
   ```bash
   # Supabase Configuration
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_KEY=your_service_role_key

   # Clerk Configuration
   CLERK_SECRET_KEY=sk_test_your_secret_key
   CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key

   # API Configuration
   API_PREFIX=/api
   BACKEND_CORS_ORIGINS=["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"]

   # Environment
   ENVIRONMENT=development

   # Clerk Webhook Configuration (optional)
   CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret
   ```

## 🔑 Where to Get Keys

### Supabase Keys
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to Settings > API
4. Copy the Project URL and anon/service_role keys

### Clerk Keys
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Select your application
3. Go to API Keys
4. Copy the Publishable Key and Secret Key

### Clerk Webhook Secret (Optional)
1. In Clerk Dashboard, go to Webhooks
2. Create or select a webhook endpoint
3. Copy the webhook secret

## ⚠️ Security Best Practices

- ✅ `.env` files are in `.gitignore`
- ✅ Use different keys for development/production
- ✅ Rotate keys regularly
- ✅ Never share keys in chat/email
- ✅ Use environment-specific configurations

## 🚨 If Keys Are Compromised

1. **Immediately rotate all keys** in respective dashboards
2. **Update all environments** with new keys
3. **Check access logs** for unauthorized usage
4. **Review recent commits** for accidental exposure

## 📝 Environment Variables Reference

| Variable | Required | Description |
|----------|----------|-------------|
| `VITE_SUPABASE_URL` | ✅ | Supabase project URL |
| `VITE_SUPABASE_ANON_KEY` | ✅ | Supabase anonymous key |
| `VITE_CLERK_PUBLISHABLE_KEY` | ✅ | Clerk publishable key |
| `VITE_API_BASE_URL` | ✅ | Backend API base URL |
| `SUPABASE_KEY` | ✅ | Supabase service role key |
| `CLERK_SECRET_KEY` | ✅ | Clerk secret key |
| `CLERK_WEBHOOK_SECRET` | ⚪ | Clerk webhook secret (optional) |
